/**
 * ملف JavaScript محسن وموحد لواجهة الهاتف المحمول - Loacker
 * يجمع جميع الوظائف المطلوبة في ملف واحد محسن
 * تم إنشاؤه: 2024
 */

// كشف نوع الجهاز وتطبيق الفئات المناسبة
class DeviceDetector {
    constructor() {
        this.detectDevice();
        this.applyDeviceClasses();
    }

    detectDevice() {
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent);

        this.deviceType = isMobile && !isTablet ? 'mobile' : isTablet ? 'tablet' : 'desktop';
        this.isIPhone = /iphone/i.test(userAgent);
        this.isXiaomi = /xiaomi|mi /i.test(userAgent);
        this.isSamsung = /samsung/i.test(userAgent);
        this.isAndroid = /android/i.test(userAgent);

        // تحديد حجم الشاشة
        this.screenWidth = window.innerWidth;
        this.isSmallPhone = this.screenWidth <= 375;
        this.isLargePhone = this.screenWidth >= 390;
    }

    applyDeviceClasses() {
        const body = document.body;

        // إزالة جميع الفئات السابقة
        body.classList.remove(
            'mobile-device', 'tablet-device', 'desktop-device',
            'small-phone', 'large-phone', 'iphone-device', 'xiaomi-device',
            'samsung-device', 'android-device'
        );

        // إضافة فئة نوع الجهاز
        body.classList.add(`${this.deviceType}-device`);

        if (this.deviceType === 'mobile') {
            // إضافة فئات حجم الشاشة
            if (this.isSmallPhone) body.classList.add('small-phone');
            if (this.isLargePhone) body.classList.add('large-phone');

            // إضافة فئات نوع الجهاز
            if (this.isIPhone) body.classList.add('iphone-device');
            if (this.isXiaomi) body.classList.add('xiaomi-device');
            if (this.isSamsung) body.classList.add('samsung-device');
            if (this.isAndroid) body.classList.add('android-device');

            // إظهار واجهة الموبايل
            const mobileView = document.querySelector('.mobile-view');
            if (mobileView) {
                mobileView.style.display = 'block';
            }

            // إخفاء واجهة الكمبيوتر
            const desktopView = document.querySelector('.container-fluid');
            if (desktopView) {
                desktopView.style.display = 'none';
            }
        }
    }
}

class MobileManager {
    constructor() {
        this.mobileMap = null;
        this.mobileSelectedLocation = null;
        this.mobileMarkers = [];
        this.mobileStores = [];
        this.mobileCurrentListId = null;
        this.mobileLists = [];

        // التحقق من أن الجهاز هو هاتف محمول
        if (!document.body.classList.contains('mobile-device')) {
            console.log('ليس جهاز محمول - لن يتم تهيئة MobileManager');
            return;
        }

        this.init();
    }

    init() {
        console.log('🚀 تهيئة مدير الهاتف المحمول...');

        // تهيئة الخريطة
        this.initMobileMap();

        // إعداد مستمعي الأحداث
        this.setupEventListeners();

        // تحميل البيانات الأولية
        this.loadInitialData();

        console.log('✅ تم تهيئة مدير الهاتف المحمول بنجاح');
    }

    initMobileMap() {
        const mapElement = document.getElementById('mobile-map');
        if (!mapElement || typeof L === 'undefined') {
            console.log('عنصر الخريطة غير موجود أو مكتبة Leaflet غير متوفرة');
            return;
        }

        try {
            this.mobileMap = L.map('mobile-map', {
                center: [32.8872, 13.1913],
                zoom: 13
            });

            // إضافة طبقات الخريطة
            const streetsLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; OpenStreetMap contributors'
            });

            const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles &copy; Esri'
            });

            const baseMaps = {
                "الخريطة": streetsLayer,
                "القمر الصناعي": satelliteLayer
            };

            streetsLayer.addTo(this.mobileMap);
            L.control.layers(baseMaps).addTo(this.mobileMap);

            // إعداد مستمعات أحداث الخريطة
            this.setupMapEventListeners();

            console.log('✅ تم تهيئة خريطة الهاتف المحمول');
        } catch (error) {
            console.error('❌ خطأ في تهيئة الخريطة:', error);
        }
    }

    setupMapEventListeners() {
        if (!this.mobileMap) return;

        this.mobileMap.on('click', (e) => {
            this.setSelectedLocation(e.latlng);
        });
    }

    setupEventListeners() {
        // مستمعي أحداث التبويبات
        this.setupTabListeners();

        // مستمعي أحداث النماذج
        this.setupFormListeners();

        // مستمعي أحداث الأزرار
        this.setupButtonListeners();
    }

    setupTabListeners() {
        const tabButtons = document.querySelectorAll('.mobile-tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.switchTab(btn.dataset.tab);
            });
        });
    }

    switchTab(tabId) {
        // إزالة الفئة النشطة من جميع الأزرار
        document.querySelectorAll('.mobile-tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // إضافة الفئة النشطة للزر المنقور
        const activeBtn = document.querySelector(`[data-tab="${tabId}"]`);
        if (activeBtn) activeBtn.classList.add('active');

        // إخفاء جميع محتويات التبويبات
        document.querySelectorAll('.mobile-tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // إظهار المحتوى المطلوب
        const tabContent = document.getElementById(tabId);
        if (tabContent) {
            tabContent.classList.add('active');

            // تحديث حجم الخريطة إذا كان التبويب هو الخريطة
            if (tabId === 'mobile-map-tab' && this.mobileMap) {
                setTimeout(() => this.mobileMap.invalidateSize(), 100);
            }
        }
    }

    setupFormListeners() {
        const storeForm = document.getElementById('mobile-store-form');
        if (storeForm) {
            storeForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit();
            });
        }

        // مستمعي أحداث حقول النموذج
        this.setupFormFieldListeners();
    }

    setupFormFieldListeners() {
        const formFields = [
            'mobile-store-name',
            'mobile-store-phone',
            'mobile-store-list'
        ];

        formFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', () => this.checkFormFields());
                field.addEventListener('focus', () => this.checkFormFields());
            }
        });

        // مستمع حدث تغيير الصورة
        const imageInput = document.getElementById('mobile-store-image');
        if (imageInput) {
            imageInput.addEventListener('change', (e) => this.handleImageChange(e));
        }
    }

    setupButtonListeners() {
        // زر الموقع الحالي
        const getCurrentLocationBtn = document.getElementById('mobile-get-location');
        if (getCurrentLocationBtn) {
            getCurrentLocationBtn.addEventListener('click', () => this.getCurrentLocation());
        }

        // زر إضافة متجر جديد
        const addStoreBtn = document.getElementById('mobile-add-store');
        if (addStoreBtn) {
            addStoreBtn.addEventListener('click', () => this.addNewStore());
        }

        // زر مسح النموذج
        const clearFormBtn = document.getElementById('mobile-clear-form');
        if (clearFormBtn) {
            clearFormBtn.addEventListener('click', () => this.resetForm());
        }
    }

    setSelectedLocation(latlng) {
        this.mobileSelectedLocation = latlng;

        // تحديث النص
        const locationText = document.getElementById('mobile-selected-location');
        if (locationText) {
            locationText.textContent = `${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
            locationText.classList.remove('text-info');
            locationText.classList.add('text-success');
        }

        // إضافة علامة على الخريطة
        this.addLocationMarker(latlng);

        // التبديل إلى تبويب النموذج
        this.switchTab('mobile-form-tab');
    }

    addLocationMarker(latlng) {
        if (!this.mobileMap) return;

        // إزالة العلامة السابقة
        this.mobileMarkers = this.mobileMarkers.filter(marker => {
            if (marker.options.isSelected) {
                this.mobileMap.removeLayer(marker);
                return false;
            }
            return true;
        });

        // إضافة علامة جديدة
        const marker = L.marker(latlng, {
            icon: L.divIcon({
                html: '<i class="fas fa-map-marker-alt" style="color: #d50000; font-size: 24px;"></i>',
                iconSize: [30, 30],
                iconAnchor: [15, 30],
                className: 'selected-marker-container'
            }),
            isSelected: true
        }).addTo(this.mobileMap);

        this.mobileMarkers.push(marker);
    }

    getCurrentLocation() {
        if (!navigator.geolocation) {
            alert('⚠️ متصفحك لا يدعم تحديد الموقع الجغرافي');
            return;
        }

        const btn = document.getElementById('mobile-get-location');
        if (btn) {
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> جاري التحديد...';
            btn.disabled = true;
        }

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;

                if (this.mobileMap) {
                    this.mobileMap.setView([lat, lng], 16, { animate: true });
                    this.setSelectedLocation(L.latLng(lat, lng));
                }

                if (btn) {
                    btn.innerHTML = '<i class="fas fa-location-arrow me-1"></i> موقعي الحالي';
                    btn.disabled = false;
                }
            },
            (error) => {
                console.error('خطأ في تحديد الموقع:', error);
                alert('⚠️ حدث خطأ في تحديد الموقع');

                if (btn) {
                    btn.innerHTML = '<i class="fas fa-location-arrow me-1"></i> موقعي الحالي';
                    btn.disabled = false;
                }
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 0
            }
        );
    }

    checkFormFields() {
        const nameInput = document.getElementById('mobile-store-name');
        const phoneInput = document.getElementById('mobile-store-phone');
        const clearButton = document.getElementById('mobile-clear-form');

        if (nameInput && phoneInput && clearButton) {
            const hasData = nameInput.value.trim() !== '' || phoneInput.value.trim() !== '';

            if (hasData) {
                clearButton.classList.remove('d-none');
            } else {
                clearButton.classList.add('d-none');
            }
        }
    }

    handleImageChange(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const imagePreview = document.getElementById('mobile-image-preview');
                if (imagePreview) {
                    const img = imagePreview.querySelector('img');
                    if (img) {
                        img.src = e.target.result;
                        imagePreview.classList.remove('d-none');
                    }
                }
            };
            reader.readAsDataURL(file);
        }

        this.checkFormFields();
    }

    resetForm() {
        const form = document.getElementById('mobile-store-form');
        if (form) form.reset();

        // إخفاء معاينة الصورة
        const imagePreview = document.getElementById('mobile-image-preview');
        if (imagePreview) imagePreview.classList.add('d-none');

        // إعادة تعيين النص
        const locationText = document.getElementById('mobile-selected-location');
        if (locationText) {
            locationText.textContent = 'لم يتم تحديد موقع';
            locationText.classList.remove('text-success');
            locationText.classList.add('text-info');
        }

        // إزالة العلامة المحددة
        this.mobileSelectedLocation = null;
        this.mobileMarkers = this.mobileMarkers.filter(marker => {
            if (marker.options.isSelected) {
                this.mobileMap.removeLayer(marker);
                return false;
            }
            return true;
        });

        this.checkFormFields();
    }

    addNewStore() {
        this.resetForm();
        this.switchTab('mobile-form-tab');
    }

    handleFormSubmit() {
        // التحقق من البيانات المطلوبة
        const name = document.getElementById('mobile-store-name').value.trim();
        const phone = document.getElementById('mobile-store-phone').value.trim();

        if (!name) {
            alert('⚠️ يرجى إدخال اسم المتجر');
            return;
        }

        if (!this.mobileSelectedLocation) {
            alert('⚠️ يرجى تحديد موقع المتجر على الخريطة');
            return;
        }

        // إعداد بيانات المتجر
        const storeData = {
            name: name,
            phone: phone,
            latitude: this.mobileSelectedLocation.lat,
            longitude: this.mobileSelectedLocation.lng,
            list_id: document.getElementById('mobile-store-list').value
        };

        // إضافة الصورة إذا وجدت
        const imageFile = document.getElementById('mobile-store-image').files[0];
        if (imageFile) {
            storeData.imageFile = imageFile;
        }

        // إرسال البيانات إلى الخادم
        this.submitStore(storeData);
    }

    async submitStore(storeData) {
        try {
            const formData = new FormData();
            Object.keys(storeData).forEach(key => {
                formData.append(key, storeData[key]);
            });

            const response = await fetch('/api/stores', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                alert('✅ تم إضافة المتجر بنجاح');
                this.resetForm();
                this.loadStores();
            } else {
                alert(`❌ خطأ: ${result.error || 'حدث خطأ أثناء إضافة المتجر'}`);
            }
        } catch (error) {
            console.error('خطأ في إرسال البيانات:', error);
            alert('❌ حدث خطأ أثناء إضافة المتجر');
        }
    }

    loadInitialData() {
        this.loadStores();
    }

    async loadStores() {
        try {
            const response = await fetch('/api/stores');
            const stores = await response.json();

            this.mobileStores = stores;
            this.renderStores();
            this.renderStoreMarkers();
        } catch (error) {
            console.error('خطأ في تحميل المتاجر:', error);
        }
    }

    renderStores() {
        const storeList = document.getElementById('mobile-store-list');
        if (!storeList) return;

        if (this.mobileStores.length === 0) {
            storeList.innerHTML = '<div class="text-center py-4"><p class="text-muted">لا توجد متاجر</p></div>';
            return;
        }

        storeList.innerHTML = '';
        this.mobileStores.forEach(store => {
            const storeEl = this.createStoreCard(store);
            storeList.appendChild(storeEl);
        });
    }

    createStoreCard(store) {
        const storeEl = document.createElement('div');
        storeEl.className = 'card mobile-store-card';
        storeEl.innerHTML = `
            <div class="card-body">
                <h5 class="card-title">${store.name || 'متجر بدون اسم'}</h5>
                <h6 class="card-subtitle">${store.phone || 'لا يوجد رقم هاتف'}</h6>
                <div class="btn-group w-100">
                    <button class="btn btn-outline-primary locate-store" data-store-id="${store.id}">
                        <i class="fas fa-map-marked-alt"></i> الموقع
                    </button>
                    <button class="btn btn-outline-success share-store" data-store-id="${store.id}">
                        <i class="fab fa-whatsapp"></i> مشاركة
                    </button>
                </div>
            </div>
        `;

        // إضافة مستمعي الأحداث
        const locateBtn = storeEl.querySelector('.locate-store');
        const shareBtn = storeEl.querySelector('.share-store');

        if (locateBtn) {
            locateBtn.addEventListener('click', () => this.locateStore(store.id));
        }

        if (shareBtn) {
            shareBtn.addEventListener('click', () => this.shareStore(store.id));
        }

        return storeEl;
    }

    renderStoreMarkers() {
        if (!this.mobileMap) return;

        // إزالة العلامات السابقة (باستثناء علامة الموقع المحدد)
        this.mobileMarkers = this.mobileMarkers.filter(marker => {
            if (!marker.options.isSelected) {
                this.mobileMap.removeLayer(marker);
                return false;
            }
            return true;
        });

        // إضافة علامات جديدة
        this.mobileStores.forEach(store => {
            if (store.latitude && store.longitude) {
                const marker = L.marker([store.latitude, store.longitude], {
                    icon: L.divIcon({
                        html: '<i class="fas fa-map-marker-alt" style="color: #d50000; font-size: 20px;"></i>',
                        iconSize: [24, 24],
                        iconAnchor: [12, 24],
                        className: 'store-marker-container'
                    }),
                    storeId: store.id
                }).addTo(this.mobileMap);

                marker.bindPopup(`
                    <div class="text-center">
                        <h6>${store.name}</h6>
                        <p>${store.phone || 'لا يوجد رقم هاتف'}</p>
                    </div>
                `);

                this.mobileMarkers.push(marker);
            }
        });
    }

    locateStore(storeId) {
        const store = this.mobileStores.find(s => s.id == storeId);
        if (!store || !this.mobileMap) return;

        if (!store.latitude || !store.longitude) {
            alert('⚠️ لا يوجد موقع مضاف لهذا المتجر');
            return;
        }

        this.switchTab('mobile-map-tab');
        this.mobileMap.setView([store.latitude, store.longitude], 16, { animate: true });

        const marker = this.mobileMarkers.find(m => m.options.storeId == storeId);
        if (marker) {
            marker.openPopup();
        }
    }

    shareStore(storeId) {
        const store = this.mobileStores.find(s => s.id == storeId);
        if (!store) return;

        if (!store.latitude || !store.longitude) {
            alert('⚠️ لا يوجد موقع لمشاركته');
            return;
        }

        const mapUrl = `https://www.google.com/maps?q=${store.latitude},${store.longitude}`;
        let message = `${store.name}\n`;
        if (store.phone) message += `${store.phone}\n`;
        message += mapUrl;

        const whatsappUrl = `whatsapp://send?text=${encodeURIComponent(message)}`;
        window.location.href = whatsappUrl;
    }
}

// تهيئة كاشف الأجهزة ومدير الهاتف المحمول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    // تهيئة كاشف الأجهزة أولاً
    window.deviceDetector = new DeviceDetector();

    // تهيئة مدير الموبايل إذا كان الجهاز هاتف محمول
    if (document.body.classList.contains('mobile-device')) {
        window.mobileManager = new MobileManager();
    }
});
