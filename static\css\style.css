/* تنسيقات عامة */
body {
    font-family: 'Ta<PERSON><PERSON>', sans-serif;
    touch-action: manipulation; /* تحسين الأداء على الأجهزة التي تعمل باللمس */
}

/* تنسيقات تسليط الضوء على الخريطة */
@keyframes mapHighlight {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4); }
    70% { box-shadow: 0 0 0 15px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

.map-highlight {
    animation: mapHighlight 1.5s ease-in-out;
    border: 2px solid #ffc107 !important;
    border-radius: 5px;
}

/* تنسيقات علامات الخريطة المميزة */
.map-marker-highlighted {
    background-color: #d50000;
    color: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    border: 2px solid white;
    z-index: 1000;
}

/* تأثير النبض للعلامة المميزة */
.pulse-animation {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(213, 0, 0, 0.7);
    }

    70% {
        transform: scale(1.2);
        box-shadow: 0 0 0 10px rgba(213, 0, 0, 0);
    }

    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(213, 0, 0, 0);
    }
}

/* تنسيقات تسليط الضوء على خريطة الهاتف */
.mobile-map-highlight {
    animation: mapHighlight 1.5s ease-in-out;
    border: 2px solid #ffc107 !important;
    border-radius: 5px;
}

/* تنسيقات زر مسح النموذج */
#clearForm, #mobileClearForm, #mobile-clear-form {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
}

/* تنسيقات زر التعديل */
#editStore, #mobileEditStore, #mobile-edit-store {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
}

/* تنسيقات شعار Loacker */
.logo-title-container {
    display: flex;
    justify-content: center;
    margin-bottom: 1rem;
    position: relative;
}

/* وعاء الشعار والعنوان */
.logo-title-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تموضع الشعار بعيداً عن الاسم */
.logo-circle-small {
    position: absolute;
    right: -80px; /* تقريب الشعار قليلاً إلى الاسم */
    top: 50%;
    transform: translateY(-50%);
}

/* تنسيقات الشعار */
.logo-circle-small {
    width: 55px;
    height: 55px;
    border-radius: 8px; /* مربع بحواف دائرية مثل شعار Loacker */
    background-color: white; /* خلفية بيضاء مثل شعار Loacker */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    z-index: 1;
    border: 1px solid #f5f5f5;
}

.logo-circle-small::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.5), rgba(255,255,255,0));
    z-index: -1;
    border-radius: 8px;
}

.logo-circle-small:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.15);
}

.logo-circle-small i {
    font-size: 2rem;
    color: #d50000; /* لون شعار Loacker الأحمر */
    transition: all 0.3s ease;
}

.logo-circle-small:hover i {
    transform: scale(1.1);
}

/* تنسيقات النص لشعار Loacker */
.text-gradient {
    color: #d50000; /* لون شعار Loacker الأحمر */
    display: inline-block;
    padding: 0.2rem 0;
    font-size: 3.5rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    font-family: 'Arial', sans-serif; /* خط مشابه لشعار Loacker */
    /* إزالة تحويل النص إلى حروف كبيرة */
    position: relative;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* تنسيق حرف L ليشبه الخطوط الكلاسيكية الأوروبية */
.text-gradient::first-letter {
    font-size: 4.5rem;
    font-weight: 900;
    font-family: 'Playfair Display', serif;
    font-style: italic;
    margin-right: 2px;
    vertical-align: -5%;
    line-height: 0.8;
    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.15);
}

/* تم إزالة الخط من تحت الاسم */

/* تنسيقات الخريطة */
#map, #mobile-map {
    touch-action: pan-x pan-y; /* السماح بالتحريك في كلا الاتجاهين */
    cursor: grab; /* مؤشر الماوس للإشارة إلى إمكانية التحريك */
}

#map:active, #mobile-map:active {
    cursor: grabbing; /* تغيير مؤشر الماوس عند السحب */
}

/* تنسيقات متحكم طبقات الخريطة */
/* تنسيقات متحكم طبقات الخريطة */
.leaflet-control-layers {
    border-radius: var(--border-radius) !important;
    overflow: hidden !important;
    border: none !important;
}

/* زر تبديل طبقات الخريطة */
.leaflet-control-layers-toggle {
    width: 28px !important;
    height: 28px !important;
    border-radius: 2px !important;
    background-color: white !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #333 !important;
    background-image: none !important;
    position: relative !important;
    overflow: hidden !important;
    margin: 8px !important;
}

.leaflet-control-layers-toggle::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.4)) !important;
    z-index: 0 !important;
}

/* زر طبقات الخريطة مشابه لجوجل مابس */
.leaflet-control-layers-toggle {
    position: relative !important;
    overflow: hidden !important;
}

.leaflet-control-layers-toggle::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: white !important;
    z-index: 0 !important;
}

/* طبقة الخريطة العادية */
.leaflet-control-layers-toggle::after {
    content: '' !important;
    position: absolute !important;
    top: 6px !important;
    left: 6px !important;
    right: 6px !important;
    height: 6px !important;
    background: #f1f3f4 !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 1px !important;
    z-index: 1 !important;
}

/* طبقة القمر الصناعي */
.leaflet-control-layers-toggle .satellite-layer {
    position: absolute !important;
    top: 15px !important;
    left: 6px !important;
    right: 6px !important;
    height: 6px !important;
    background: #4285F4 !important; /* لون جوجل الأزرق */
    border: 1px solid #3367d6 !important;
    border-radius: 1px !important;
    z-index: 1 !important;
}

/* إضافة ظل للطبقات */
.leaflet-control-layers-toggle::before {
    content: '' !important;
    position: absolute !important;
    top: 4px !important;
    left: 4px !important;
    right: 4px !important;
    bottom: 4px !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) inset !important;
    border-radius: 1px !important;
    z-index: 0 !important;
}

.leaflet-control-layers-toggle:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    background-color: #f8f8f8 !important;
}

.leaflet-control-layers-toggle:active {
    transform: translateY(0) !important;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2) !important;
    background-color: #f0f0f0 !important;
}

/* قائمة طبقات الخريطة الموسعة */
.leaflet-control-layers-expanded {
    padding: 8px !important;
    background-color: white !important;
    border-radius: 4px !important;
    min-width: 150px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    border: none !important;
    transform: translateY(0) !important;
    margin-top: 3px !important;
    animation: fadeIn 0.2s ease-in-out !important;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تنسيقات خيارات الطبقات */
.leaflet-control-layers-base label {
    padding: 6px 8px !important;
    border-radius: 3px !important;
    margin-bottom: 3px !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    background-color: white !important;
    box-shadow: none !important;
    font-size: 12px !important;
    color: #333 !important;
    cursor: pointer !important;
}

.leaflet-control-layers-base label:hover {
    background-color: #f1f3f4 !important; /* لون خلفية مشابه لجوجل ماب */
    transform: none !important;
    box-shadow: none !important;
}

.leaflet-control-layers-base label:active {
    background-color: #e8eaed !important;
    transform: none !important;
    box-shadow: none !important;
}

/* تنسيقات خيارات الطبقات المخصصة */
.map-layer-option {
    position: relative !important;
    overflow: hidden !important;
}

.map-layer-option::before {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    height: 100% !important;
    width: 4px !important;
    background-color: var(--primary-color) !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.map-layer-option:hover::before {
    opacity: 1 !important;
}

/* تنسيقات الخيار المحدد */
.leaflet-control-layers-selector:checked + span {
    font-weight: 500 !important;
    color: #1a73e8 !important; /* لون جوجل الأزرق */
}

.leaflet-control-layers-selector:checked + span i {
    color: #1a73e8 !important;
    transform: none !important;
}

.leaflet-control-layers-base input:checked + span {
    position: relative !important;
}

.leaflet-control-layers-base input:checked + span::after {
    content: '\f00c' !important; /* Font Awesome check icon */
    font-family: 'Font Awesome 5 Free' !important;
    font-weight: 900 !important;
    position: absolute !important;
    right: -5px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: #1a73e8 !important; /* لون جوجل الأزرق */
    font-size: 0.8rem !important;
}

.leaflet-control-layers-selector {
    margin-right: 8px !important;
}

/* تنسيقات دائرة الدقة */
.leaflet-interactive {
    transition: all 0.3s ease;
}

/* واجهات الكمبيوتر والهاتف - يتم التحكم بها بواسطة JavaScript */
.container-fluid {
    display: none; /* سيتم التحكم في العرض بواسطة JavaScript */
}

.mobile-view {
    display: none; /* سيتم التحكم في العرض بواسطة JavaScript */
}

/* تنسيقات خاصة بالأجهزة */
body.mobile-device .mobile-view {
    display: block !important; /* إظهار واجهة الهاتف على الأجهزة المحمولة */
    background-color: #f8f9fa !important;
    color: #333 !important;
}

body.desktop-device .container-fluid {
    display: block !important; /* إظهار واجهة الكمبيوتر على أجهزة الكمبيوتر */
}

/* تنسيقات أزرار القائمة */
#view-all-lists, #list-tab-direct {
    transition: all 0.3s ease;
}

#view-all-lists:hover, #list-tab-direct:hover {
    background-color: var(--bs-primary);
    color: white;
}

/* تنسيقات المتجر المحدد */
.selected-store {
    border-right: 3px solid var(--primary-color);
    background-color: rgba(0, 123, 255, 0.1);
    box-shadow: 0 3px 10px rgba(0, 123, 255, 0.15);
    transform: translateX(-5px);
    transition: all 0.3s ease;
}

.store-card {
    transition: all 0.3s ease;
}

.store-checkbox {
    cursor: pointer;
    transform: scale(1.2);
    margin-right: 5px;
}

/* تنسيقات زر إلغاء التحديد */
#cancelSelection {
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateX(10px);
    pointer-events: none;
}

#cancelSelection.show {
    opacity: 1;
    transform: translateX(0);
    pointer-events: auto;
}

/* تنسيقات أزرار الحذف والمشاركة */
#deleteSelectedStores, #shareSelectedStores {
    transition: all 0.3s ease;
    min-width: 44px; /* تحديد عرض أدنى للأزرار */
    padding: 0.375rem 0.75rem; /* تحديد المسافة الداخلية */
}

#deleteSelectedStores:not(:disabled):hover {
    background-color: var(--bs-danger);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

#shareSelectedStores:not(:disabled):hover {
    background-color: var(--bs-success);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(25, 135, 84, 0.3);
}

/* تحسين مظهر أيقونات الأزرار */
#deleteSelectedStores i, #shareSelectedStores i {
    font-size: 1rem; /* حجم الأيقونة */
}

/* تنسيقات نافذة تأكيد الحذف */
#deleteConfirmationModal .modal-content {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

#deleteConfirmationModal .modal-header {
    border-bottom: none;
}

#deleteConfirmationModal .modal-footer {
    border-top: none;
}

#deleteConfirmationCount {
    font-size: 1.2rem;
    font-weight: bold;
}

/* تنسيقات النافذة المنبثقة */
.modal-content {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: none;
}

.modal-header {
    background-color: var(--bs-primary);
    color: white;
    border-bottom: none;
    padding: 1.25rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-body img {
    max-width: 100%;
    border-radius: 8px;
}

.modal-footer {
    border-top: none;
    padding: 1rem 1.5rem 1.5rem;
}

/* تنسيقات نافذة تأكيد تغيير الموقع */
#locationChangeModal .modal-header {
    background-color: #1a73e8; /* لون جوجل ماب */
}

#locationChangeModal .fa-map-marker-alt {
    color: #1a73e8;
    margin-bottom: 1rem;
}

#confirmLocationChange {
    background-color: #1a73e8;
    border-color: #1a73e8;
    transition: all 0.3s ease;
}

#confirmLocationChange:hover {
    background-color: #0d62c9;
    border-color: #0d62c9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(26, 115, 232, 0.3);
}

#list-tab-direct {
    position: relative;
}

#list-tab-direct::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--bs-primary);
    transition: width 0.3s ease;
}

#list-tab-direct:hover::after {
    width: 100%;
}

.store-list-header .btn-group {
    margin-right: 10px;
}

/* تأثيرات الانتقال */
.list-transition {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* تنسيقات الخريطة الرئيسية - محسنة للواجهة */
#map {
    height: 500px;
    width: 100%;
    border-radius: 0;
    border: none;
    background-color: #f8f9fa;
    position: relative;
    overflow: hidden;
}

/* تحسين عرض الخريطة داخل البطاقة */
.tab-pane #map {
    height: calc(100vh - 280px);
    min-height: 400px;
    max-height: 600px;
}

/* تنسيقات خاصة عندما تكون الخريطة في تبويب نشط */
.tab-pane.show.active #map {
    height: calc(100vh - 280px);
    min-height: 450px;
}

/* تحسين الخريطة للشاشات الكبيرة */
@media (min-width: 1200px) {
    .tab-pane #map {
        height: calc(100vh - 250px);
        min-height: 500px;
    }
}

/* تحسين الخريطة للشاشات المتوسطة */
@media (min-width: 768px) and (max-width: 1199px) {
    .tab-pane #map {
        height: calc(100vh - 300px);
        min-height: 400px;
    }
}

/* تحسين الخريطة للشاشات الصغيرة */
@media (max-width: 767px) {
    .tab-pane #map {
        height: 350px;
        min-height: 300px;
    }
}

/* تنسيقات خريطة الموبايل */
#mobile-map {
    height: 500px;
    width: 100%;
}

/* تحسين حاوي البطاقة للخريطة */
.card-body.p-0 {
    padding: 0 !important;
}

.card-body.p-0 .tab-content {
    height: 100%;
}

.card-body.p-0 .tab-pane {
    height: 100%;
}

/* تحسين عرض الخريطة عند تغيير التبويبات */
.tab-pane.fade:not(.show) {
    display: none;
}

.tab-pane.fade.show {
    display: block;
}

/* إضافة تأثير انتقال سلس للخريطة */
#map {
    transition: all 0.3s ease;
}

/* تحسين عرض الخريطة عند تحميل الصفحة */
.tab-pane.show.active #map {
    opacity: 1;
    visibility: visible;
}

/* تنسيقات قائمة المتاجر */
/* تنسيقات شريط التمرير المستقل */
.store-list-container {
    position: relative;
    height: calc(100vh - 280px); /* نفس ارتفاع الخريطة */
    min-height: 400px;
    max-height: 600px;
    overflow: hidden;
}

/* تحسين قائمة المتاجر للشاشات المختلفة */
@media (min-width: 1200px) {
    .store-list-container {
        height: calc(100vh - 250px);
        min-height: 500px;
    }
}

@media (min-width: 768px) and (max-width: 1199px) {
    .store-list-container {
        height: calc(100vh - 300px);
        min-height: 400px;
    }
}

@media (max-width: 767px) {
    .store-list-container {
        height: 350px;
        min-height: 300px;
    }
}

.store-list-scrollable {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow-y: auto;
    padding: 1rem;
    scrollbar-width: thin;
    scrollbar-color: var(--bs-primary) var(--bs-dark);
}

.store-list-scrollable::-webkit-scrollbar {
    width: 8px;
}

.store-list-scrollable::-webkit-scrollbar-track {
    background: var(--bs-dark);
    border-radius: 4px;
}

.store-list-scrollable::-webkit-scrollbar-thumb {
    background-color: var(--bs-primary);
    border-radius: 4px;
}

.store-list-header {
    background-color: rgba(var(--bs-dark-rgb), 0.8);
    backdrop-filter: blur(5px);
    position: sticky;
    top: 0;
    z-index: 10;
}

.store-card {
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
    margin-bottom: 1rem;
}

.store-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-right: 3px solid var(--bs-primary);
}

.store-image {
    margin-right: 10px;
    position: relative;
}

.store-image img {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.store-image img:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* تنسيقات معاينة الصورة */
.image-preview {
    position: relative;
    display: inline-block;
    max-width: 200px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.image-preview img {
    width: 100%;
    height: auto;
    display: block;
}

.btn-remove-image {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: rgba(255, 255, 255, 0.7);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--bs-danger);
    transition: all 0.2s ease;
}

.btn-remove-image:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: scale(1.1);
}

/* تنسيقات العلامات على الخريطة */
.selected-marker {
    color: var(--bs-danger);
    font-size: 28px;
    filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.5));
}

.store-marker {
    color: #d50000;
    font-size: 24px;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.4));
}

.store-marker:hover {
    transform: scale(1.2);
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.6));
}

/* تنسيقات دبوس الخريطة */
.fa-map-marker-alt.store-marker {
    animation: google-bounce 1.5s infinite;
}

@keyframes google-bounce {
    0%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-7px);
    }
}

.marker-pin .fa-map-marker-alt {
    animation: google-pulse 2s infinite;
}

@keyframes google-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* تأثير الظل لدبوس الخريطة */
.marker-pin {
    position: relative;
}

.marker-pin::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 10px;
    height: 3px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    filter: blur(2px);
}

/* تنسيقات تمييز نتائج البحث */
.highlight {
    background-color: var(--bs-warning);
    color: var(--bs-dark);
    padding: 0 3px;
    border-radius: 3px;
}

/* تنسيقات المتاجر المحددة */
.selected-store {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    border-right: 3px solid var(--bs-primary);
}

/* تنسيقات الشاشات المتوسطة - لواجهة الكمبيوتر فقط */
@media (max-width: 991.98px) {
    .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .store-list-header h5 {
        font-size: 1rem;
    }

    .store-list-header .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .store-image {
        margin-right: 5px;
    }

    .store-image img {
        width: 60px;
        height: 60px;
    }
}

/* تنسيقات واجهة الهاتف النقال */
.mobile-view {
    padding: 0;
}

.mobile-view .mobile-header {
    background-color: white;
    color: #333;
    padding: 15px 20px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

.mobile-view .mobile-header h1 {
    font-size: 2rem;
    margin: 0;
    font-weight: 700;
    letter-spacing: -0.5px;
    color: #d50000; /* لون شعار Loacker الأحمر */
    position: relative;
    font-family: 'Arial', sans-serif;
    /* إزالة تحويل النص إلى حروف كبيرة */
}

/* وعاء الشعار والعنوان في واجهة الهاتف */
.mobile-logo-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

/* شعار في واجهة الهاتف */
.logo-circle-small-mobile {
    width: 35px;
    height: 35px;
    border-radius: 6px;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    z-index: 1;
    border: 1px solid #f5f5f5;
    position: absolute;
    right: -50px; /* تقريب الشعار قليلاً إلى الاسم */
    top: 50%;
    transform: translateY(-50%);
}

.logo-circle-small-mobile i {
    font-size: 1.4rem;
    color: #d50000;
}

/* تنسيق حرف L ليشبه الخطوط الكلاسيكية الأوروبية في واجهة الهاتف */
.mobile-view .mobile-header h1::first-letter {
    font-size: 2.6rem;
    font-weight: 900;
    font-family: 'Playfair Display', serif;
    font-style: italic;
    margin-right: 2px;
    vertical-align: -5%;
    line-height: 0.8;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.15);
}

/* تم إزالة الخط من تحت الاسم في واجهة الهاتف */

.mobile-view .mobile-content {
    margin-top: 70px;
    padding-bottom: 70px; /* للشريط السفلي */
}

.mobile-view .mobile-map {
    height: 40vh;
    width: 100%;
    z-index: 1;
}

.mobile-view .mobile-tabs {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--bs-dark);
    display: flex;
    justify-content: space-around;
    padding: 10px 0;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
}

.mobile-view .mobile-tab-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--bs-secondary);
    text-decoration: none;
    font-size: 0.8rem;
    padding: 5px 0;
    border: none;
    background: transparent;
    width: 33%;
}

.mobile-view .mobile-tab-btn.active {
    color: var(--bs-primary);
}

.mobile-view .mobile-tab-btn i {
    font-size: 1.2rem;
    margin-bottom: 4px;
}

.mobile-view .mobile-tab-content {
    display: none;
    padding: 15px;
}

.mobile-view .mobile-tab-content.active {
    display: block;
}

.mobile-view .mobile-store-list {
    max-height: calc(100vh - 60px - 60px);
    overflow-y: auto;
    padding: 10px;
}

.mobile-view .mobile-store-card {
    margin-bottom: 10px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.mobile-view .mobile-store-card .card-body {
    padding: 12px;
}

.mobile-view .mobile-store-card .card-title {
    font-size: 1rem;
    margin-bottom: 5px;
}

.mobile-view .mobile-store-card .card-subtitle {
    font-size: 0.8rem;
    margin-bottom: 8px;
}

.mobile-view .mobile-store-card .btn-group {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.mobile-view .mobile-store-card .btn {
    flex: 1 0 calc(50% - 5px);
    font-size: 0.75rem;
    padding: 5px;
}

.mobile-view .mobile-store-image {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    object-fit: cover;
}

.mobile-view .mobile-store-image-thumbnail {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid var(--bs-primary);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.mobile-view .mobile-store-image-thumbnail:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* تنسيقات النافذة المنبثقة للصورة */
#imageModal .modal-body {
    padding: 0;
}

#imageModal .modal-body img {
    max-height: 70vh;
    width: auto;
    margin: 0 auto;
}

.mobile-view .mobile-form {
    padding: 15px;
}

.mobile-view .mobile-form .form-label {
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.mobile-view .mobile-form .form-control {
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.mobile-view .mobile-form .btn {
    width: 100%;
    margin-top: 10px;
}

.mobile-view .mobile-search {
    position: relative;
    margin-bottom: 15px;
    padding: 0 10px;
}

.mobile-view .mobile-search .input-group {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.mobile-view .mobile-search .form-control {
    border-radius: 0;
    border-right: none;
}

.mobile-view .mobile-search .btn {
    border-radius: 0;
}

.mobile-view .mobile-fab {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background-color: var(--bs-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
    z-index: 900;
    border: none;
}

.mobile-view .mobile-fab i {
    font-size: 1.5rem;
}

/* تنسيقات الأزرار المعطلة بسبب الصلاحيات */
.disabled-button {
    opacity: 0.65;
    position: relative;
    overflow: hidden;
    cursor: not-allowed !important;
    pointer-events: auto !important; /* السماح بالنقر لإظهار رسالة */
}

.disabled-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.05);
    z-index: 1;
}

.disabled-button:hover {
    transform: none !important;
    box-shadow: none !important;
    background-color: inherit !important;
    color: inherit !important;
}

/* تأثير الاهتزاز للأزرار المعطلة عند النقر عليها */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake-animation {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

/* تنسيقات حقول النموذج المعطلة */
.disabled-input {
    opacity: 0.65;
    background-color: #f8f9fa;
    cursor: not-allowed !important;
    pointer-events: auto !important; /* السماح بالنقر لإظهار رسالة */
}

.disabled-input:hover, .disabled-input:focus {
    border-color: #ced4da !important;
    box-shadow: none !important;
}

/* تنسيقات للأزرار المعطلة في واجهة الهاتف */
.mobile-tab-btn.disabled-button {
    color: rgba(108, 117, 125, 0.65) !important;
}

.mobile-tab-btn.disabled-button i {
    opacity: 0.65;
}

/* تنسيقات قائمة المدن المنسدلة */
#citySelect, #mobile-city-select, #mobileCitySelect {
    cursor: pointer;
    background-color: var(--bs-dark);
    color: white;
    border-color: #495057;
}

/* تنسيقات خيارات القائمة المنسدلة */
#citySelect option, #mobile-city-select option, #mobileCitySelect option,
#districtSelect option, #mobile-district-select option, #mobileDistrictSelect option {
    padding: 8px;
    background-color: var(--bs-dark);
    color: white;
}

/* تنسيقات مجموعات الخيارات */
optgroup {
    font-weight: bold;
    color: #d50000;
    background-color: var(--bs-dark);
    padding: 5px;
}

/* تحسين مظهر القائمة المنسدلة عند التركيز */
#citySelect:focus, #mobile-city-select:focus, #mobileCitySelect:focus,
#districtSelect:focus, #mobile-district-select:focus, #mobileDistrictSelect:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    border-color: #0d6efd;
    outline: none;
}

/* تنسيقات قائمة المناطق */
#districtSelect, #mobile-district-select, #mobileDistrictSelect {
    cursor: pointer;
    background-color: var(--bs-dark);
    color: white;
    border-color: #495057;
}

/* تنسيقات النص الافتراضي للمنطقة غير المحددة */
#districtSelect option[value=""],
#mobile-district-select option[value=""],
#mobileDistrictSelect option[value=""] {
    color: #dc3545 !important; /* اللون الأحمر */
    font-weight: 500;
}

/* تنسيقات النص في صناديق البيانات عندما تكون المنطقة غير محددة */
.store-location-info .text-muted:contains("المنطقة غير محددة"),
.store-popup .text-muted:contains("المنطقة غير محددة") {
    color: #dc3545 !important; /* اللون الأحمر */
    font-weight: 500;
}

/* تنسيقات إضافية للنص الأحمر في المناطق غير المحددة */
.undefined-region-text {
    color: #dc3545 !important;
    font-weight: 500 !important;
}

/* تنسيقات للقوائم المنسدلة عندما تكون القيمة فارغة */
select option[value=""]:first-child {
    color: #dc3545 !important;
    font-weight: 500;
}

/* تنسيقات قائمة المناطق المعطلة */
#districtSelect:disabled, #mobile-district-select:disabled, #mobileDistrictSelect:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* إصلاح موضع القائمة المنسدلة للمناطق */
select.form-select, select.form-control {
    position: relative;
}

/* تحديد موضع القائمة المنسدلة لتظهر أسفل الصندوق */
.form-select option, .form-control option,
.form-select optgroup, .form-control optgroup {
    position: relative;
    display: block;
}

/* إصلاح مشكلة ظهور القائمة المنسدلة فوق الصندوق */
select {
    appearance: menulist !important;
    -webkit-appearance: menulist !important;
    -moz-appearance: menulist !important;
}

/* تأكيد على موضع القائمة المنسدلة */
#districtSelect, #mobile-district-select, #mobileDistrictSelect {
    appearance: menulist !important;
    -webkit-appearance: menulist !important;
    -moz-appearance: menulist !important;
}

/* تنسيقات حقل البحث في القائمة المنسدلة */
.district-search-container {
    position: relative;
    margin-bottom: 5px;
}

.district-search-input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #495057;
    border-radius: 4px;
    background-color: var(--bs-dark);
    color: white;
    font-size: 14px;
    direction: rtl;
}

.district-search-input:focus {
    outline: none;
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.district-search-input::placeholder {
    color: #adb5bd;
}

/* تنسيقات حاوية القائمة المنسدلة المخصصة */
.custom-select-container {
    position: relative;
    width: 100%;
}

.custom-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    max-height: 300px;
    overflow-y: auto;
    background-color: var(--bs-dark);
    border: 1px solid #495057;
    border-radius: 4px;
    z-index: 1000;
    display: none;
    margin-top: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.custom-select-dropdown.show {
    display: block;
}

.custom-select-group {
    padding: 8px 12px;
    font-weight: bold;
    color: #d50000;
    border-bottom: 1px solid #495057;
}

.custom-select-option {
    padding: 8px 12px;
    cursor: pointer;
    color: white;
}

.custom-select-option:hover {
    background-color: #495057;
}

.custom-select-option.hidden {
    display: none;
}
