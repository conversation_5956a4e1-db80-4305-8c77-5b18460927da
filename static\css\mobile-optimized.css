/**
 * ملف CSS محسن وموحد لواجهة الهاتف المحمول - Loacker
 * يجمع جميع التنسيقات المطلوبة في ملف واحد محسن
 * تم إنشاؤه: 2024
 */

/* ===== المتغيرات الأساسية ===== */
:root {
    --loacker-red: #d50000;
    --loacker-red-light: #ff5131;
    --loacker-red-dark: #9b0000;
    --header-height: 60px;
    --footer-height: 60px;
    --content-padding: 15px;
    --card-border-radius: 12px;
    --button-border-radius: 8px;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 6px 16px rgba(0, 0, 0, 0.2);
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --device-font-family: "Tajawal", "Cairo", sans-serif;
    --safe-area-inset-top: 0px;
    --safe-area-inset-bottom: 0px;
    --font-size-multiplier: 1;
}

/* ===== الهيكل الأساسي ===== */
.mobile-view {
    padding: 0;
    background-color: #f8f9fa !important;
    color: #333 !important;
    font-family: var(--device-font-family);
    height: 100vh;
    overflow-x: hidden;
    display: none;
}

body.mobile-device .mobile-view {
    display: block !important;
}

body.desktop-device .container-fluid {
    display: block !important;
}

/* ===== الرأس ===== */
.mobile-header {
    background-color: white !important;
    color: #333 !important;
    padding: 0 20px;
    height: var(--header-height);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: var(--shadow-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

.mobile-logo-wrapper h1 {
    font-size: 1.8rem;
    margin: 0;
    font-weight: 700;
    color: var(--loacker-red) !important;
    font-family: 'Arial', sans-serif;
}

/* ===== المحتوى ===== */
.mobile-content {
    margin-top: var(--header-height);
    padding-bottom: calc(var(--footer-height) + 10px);
    min-height: calc(100vh - var(--header-height) - var(--footer-height));
    background-color: #f8f9fa !important;
    color: #333 !important;
}

.mobile-tab-content {
    display: none;
    padding: var(--content-padding);
    animation: fadeIn var(--transition-normal);
}

.mobile-tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* ===== الخريطة ===== */
.mobile-map {
    height: 45vh;
    width: 100%;
    border-radius: var(--card-border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    margin-bottom: 15px;
    border: 1px solid #eee !important;
}

/* ===== بطاقات المتاجر ===== */
.mobile-store-card {
    background-color: white !important;
    color: #333 !important;
    border-radius: var(--card-border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    margin-bottom: 15px;
    border: none;
    transition: all var(--transition-normal);
    border-right: 3px solid transparent;
}

.mobile-store-card:active {
    transform: scale(0.98);
    box-shadow: var(--shadow-md);
}

.mobile-store-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333 !important;
}

.mobile-store-card .card-subtitle {
    font-size: 0.9rem;
    color: #666 !important;
    margin-bottom: 10px;
}

/* ===== الأزرار ===== */
.mobile-tab-btn {
    background-color: white !important;
    color: #666 !important;
    border: none;
    border-radius: 0;
    padding: 0.75rem 0;
    font-size: 0.85rem;
    transition: all var(--transition-normal);
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    position: relative;
    overflow: hidden;
}

.mobile-tab-btn.active {
    color: var(--loacker-red) !important;
}

.mobile-tab-btn:active {
    background-color: #f5f5f5;
    transform: scale(0.95);
}

.mobile-store-card .btn-outline-primary {
    color: var(--loacker-red) !important;
    border-color: var(--loacker-red) !important;
}

.mobile-store-card .btn-outline-primary:hover,
.mobile-store-card .btn-outline-primary:active {
    background-color: var(--loacker-red) !important;
    color: white !important;
}

/* ===== النماذج ===== */
.mobile-form .form-label {
    color: #555 !important;
    font-size: 0.9rem;
    font-weight: 500;
    margin-bottom: 5px;
}

.mobile-form .form-control,
.mobile-form .form-select {
    background-color: white !important;
    color: #333 !important;
    border-color: #eee !important;
    font-size: 0.95rem;
    padding: 10px 15px;
    border-radius: var(--button-border-radius);
    box-shadow: var(--shadow-sm);
    margin-bottom: 15px;
    transition: all var(--transition-fast);
}

.mobile-form .btn-primary {
    background-color: var(--loacker-red) !important;
    border-color: var(--loacker-red) !important;
    color: white !important;
    padding: 12px;
    font-weight: 500;
    border-radius: var(--button-border-radius);
    transition: all var(--transition-fast);
}

/* ===== شريط التبويبات السفلي ===== */
.mobile-tabs {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--footer-height);
    background-color: white !important;
    display: flex;
    justify-content: space-around;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    padding: 0;
    border-top: 1px solid #eee !important;
}

/* ===== تحسينات للأجهزة المختلفة ===== */
body.mobile-device.small-phone {
    font-size: calc(13px * var(--font-size-multiplier));
}

body.mobile-device.large-phone {
    font-size: calc(15px * var(--font-size-multiplier));
}

/* أجهزة iPhone */
body.mobile-device.iphone-device {
    --device-font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", sans-serif;
}

.mobile-view.iphone-view button,
.mobile-view.iphone-view .btn {
    border-radius: 10px;
}

/* أجهزة Xiaomi */
body.mobile-device.xiaomi-device {
    --device-font-family: "Mi Sans", "Roboto", sans-serif;
}

.mobile-view.xiaomi-view button,
.mobile-view.xiaomi-view .btn {
    border-radius: 6px;
}

/* ===== تحسينات الأداء ===== */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

button, .btn {
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

/* تأثير الموجة عند الضغط */
button:active::after, .btn:active::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.3);
    opacity: 0;
    border-radius: 100%;
    transform: translate(-50%, -50%) scale(1);
    animation: ripple 0.8s ease-out;
}

@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0.5;
    }
    100% {
        transform: translate(-50%, -50%) scale(30);
        opacity: 0;
    }
}

/* ===== استعلامات الوسائط ===== */
@media (max-width: 767.98px) {
    body.mobile-device {
        overflow-x: hidden;
    }
    
    .mobile-view {
        width: 100%;
        max-width: 100%;
        margin: 0;
        padding: 0;
    }
}

/* تحسينات للشاشات الصغيرة جداً */
@media (max-width: 375px) {
    .mobile-view.small-phone-view h1 {
        font-size: 1.5rem;
    }
    
    .mobile-view.small-phone-view #mobile-map {
        height: 250px;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 390px) {
    .mobile-view.large-phone-view #mobile-map {
        height: 350px;
    }
}
