/**
 * StoreManager class for managing store data and UI interactions
 */
class StoreManager {
    /**
     * Initialize the store manager
     * @param {StoreMap} map - The StoreMap instance
     */
    constructor(map) {
        console.log('Initializing StoreManager');
        this.map = map;
        this.stores = [];
        this.storeListContainer = document.getElementById('storeList');
        console.log('Store list container:', this.storeListContainer);

        // القائمة الحالية المحددة
        this.currentListId = null;

        // المنطقة الحالية المحددة
        this.currentRegion = null;

        // التحقق من وجود معلمة المنطقة في عنوان URL
        const urlParams = new URLSearchParams(window.location.search);
        const regionParam = urlParams.get('region');
        if (regionParam) {
            this.currentRegion = decodeURIComponent(regionParam);
            console.log(`Region parameter found in URL: ${this.currentRegion}`);
        }

        // التحقق من وجود حاوية قائمة المتاجر
        if (!this.storeListContainer) {
            console.error('Store list container not found! Trying to find it again...');
            // محاولة العثور على الحاوية بعد تحميل الصفحة
            setTimeout(() => {
                this.storeListContainer = document.getElementById('storeList');
                console.log('Store list container (retry):', this.storeListContainer);
            }, 500);
        }

        // تحميل القوائم المتاحة
        this.loadAvailableLists();

        // إعداد مستمعي الأحداث لأزرار القوائم
        this.setupListTabs();
    }

    /**
     * تحميل القوائم المتاحة من الخادم
     */
    loadAvailableLists() {
        console.log('Loading available lists...');
        fetch('/api/custom-lists')
            .then(response => response.json())
            .then(data => {
                // التحقق من بنية البيانات المستلمة
                const lists = data.lists || data;
                console.log('Available lists:', lists);

                // تحديث القائمة المنسدلة في نموذج إضافة المتجر
                const storeListSelect = document.getElementById('storeListSelect');
                if (storeListSelect) {
                    // حفظ القيمة الحالية
                    const currentValue = storeListSelect.value;

                    // إفراغ القائمة
                    storeListSelect.innerHTML = '';

                    // إضافة القوائم المتاحة
                    lists.forEach(list => {
                        const option = document.createElement('option');
                        option.value = list.id;
                        option.textContent = list.name;
                        storeListSelect.appendChild(option);
                    });

                    // إعادة تعيين القيمة الحالية إذا كانت موجودة في القوائم المتاحة
                    if (lists.some(list => list.id == currentValue)) {
                        storeListSelect.value = currentValue;
                    }
                }

                // تحديث القائمة المنسدلة في نموذج الهاتف المحمول
                const mobileStoreList = document.getElementById('mobile-store-list');
                if (mobileStoreList) {
                    // حفظ القيمة الحالية
                    const currentMobileValue = mobileStoreList.value;

                    // إفراغ القائمة
                    mobileStoreList.innerHTML = '';

                    // إضافة القوائم المتاحة
                    lists.forEach(list => {
                        const option = document.createElement('option');
                        option.value = list.id;
                        option.textContent = list.name;
                        mobileStoreList.appendChild(option);
                    });

                    // إعادة تعيين القيمة الحالية إذا كانت موجودة في القوائم المتاحة
                    if (lists.some(list => list.id == currentMobileValue)) {
                        mobileStoreList.value = currentMobileValue;
                    }
                }

                // تحديث قائمة القوائم المنسدلة في التبويب
                const listDropdownMenu = document.getElementById('list-dropdown-menu');
                if (listDropdownMenu) {
                    // إفراغ القائمة مع الاحتفاظ بالعنصر الأخير (جميع القوائم)
                    const allListsItem = listDropdownMenu.querySelector('#list-tab-all').parentNode;
                    const divider = listDropdownMenu.querySelector('hr').parentNode;
                    listDropdownMenu.innerHTML = '';

                    // إضافة القوائم المتاحة
                    lists.forEach(list => {
                        const li = document.createElement('li');
                        const button = document.createElement('button');
                        button.className = 'dropdown-item';
                        button.id = `list-tab-${list.id}`;
                        button.setAttribute('data-bs-toggle', 'tab');
                        button.setAttribute('data-bs-target', '#list-content');
                        button.setAttribute('data-list-id', list.id);
                        button.setAttribute('type', 'button');
                        button.setAttribute('role', 'tab');
                        button.textContent = list.name;

                        // إضافة مستمع الحدث
                        button.addEventListener('click', (e) => {
                            const listId = parseInt(e.target.dataset.listId);
                            this.currentListId = listId;
                            console.log(`Switching to list ${listId}`);
                            this.loadStores(listId);

                            // تحديث عنوان القائمة
                            const listTitle = document.querySelector('.store-list-header h5');
                            if (listTitle) {
                                listTitle.textContent = list.name;
                            }
                        });

                        li.appendChild(button);
                        listDropdownMenu.appendChild(li);
                    });

                    // إضافة الفاصل وعنصر جميع القوائم
                    listDropdownMenu.appendChild(divider);
                    listDropdownMenu.appendChild(allListsItem);

                    // إعادة إضافة مستمع الحدث لزر جميع القوائم
                    const allListsButton = listDropdownMenu.querySelector('#list-tab-all');
                    if (allListsButton) {
                        allListsButton.addEventListener('click', () => {
                            this.currentListId = null;
                            console.log('Switching to all lists');
                            this.loadStores();

                            // تحديث عنوان القائمة
                            const listTitle = document.querySelector('.store-list-header h5');
                            if (listTitle) {
                                listTitle.textContent = 'جميع المتاجر';
                            }
                        });
                    }
                }

                // تحديث قائمة القوائم السريعة في صفحة قائمة المتاجر
                const quickListMenu = document.getElementById('quick-list-menu');
                if (quickListMenu) {
                    // إفراغ القائمة
                    quickListMenu.innerHTML = '';

                    // إضافة القوائم المتاحة
                    lists.forEach(list => {
                        const li = document.createElement('li');
                        const button = document.createElement('button');
                        button.className = 'dropdown-item';
                        button.textContent = list.name;
                        button.setAttribute('data-list-id', list.id);

                        // إضافة مستمع الحدث
                        button.addEventListener('click', () => {
                            this.currentListId = list.id;
                            console.log(`Quick switching to list ${list.id}`);
                            this.loadStores(list.id);

                            // تحديث عنوان القائمة
                            const listTitle = document.querySelector('.store-list-header h5');
                            if (listTitle) {
                                listTitle.textContent = list.name;
                            }

                            // تحديث حالة الزر الرئيسي
                            const viewAllListsBtn = document.getElementById('view-all-lists');
                            if (viewAllListsBtn) {
                                viewAllListsBtn.classList.remove('active');
                                viewAllListsBtn.classList.add('btn-outline-primary');
                                viewAllListsBtn.classList.remove('btn-primary');
                                viewAllListsBtn.innerHTML = `<i class="fas fa-list-ul me-1"></i> <span class="d-none d-sm-inline-block">${list.name}</span>`;
                            }
                        });

                        li.appendChild(button);
                        quickListMenu.appendChild(li);
                    });
                }

                // إضافة مستمع الحدث لزر جميع القوائم الجديد
                const viewAllListsBtn = document.getElementById('view-all-lists');
                if (viewAllListsBtn) {
                    // إزالة مستمعي الأحداث السابقة
                    const newViewAllListsBtn = viewAllListsBtn.cloneNode(true);
                    viewAllListsBtn.parentNode.replaceChild(newViewAllListsBtn, viewAllListsBtn);

                    // إضافة مستمع الحدث الجديد
                    newViewAllListsBtn.addEventListener('click', () => {
                        this.currentListId = null;
                        console.log('Switching to all lists from quick button');
                        this.loadStores();

                        // تحديث عنوان القائمة
                        const listTitle = document.querySelector('.store-list-header h5');
                        if (listTitle) {
                            listTitle.textContent = 'جميع المتاجر';
                        }

                        // تحديث حالة الزر
                        newViewAllListsBtn.classList.add('active');
                        newViewAllListsBtn.classList.remove('btn-outline-primary');
                        newViewAllListsBtn.classList.add('btn-primary');

                        // تحديث نص الزر
                        newViewAllListsBtn.innerHTML = '<i class="fas fa-list-ul me-1"></i> <span class="d-none d-sm-inline-block">جميع القوائم</span>';
                    });
                }
            })
            .catch(error => console.error('Error loading lists:', error));
    }

    /**
     * إعداد مستمعي الأحداث لأزرار القوائم
     */
    setupListTabs() {
        // إضافة مستمعي الأحداث لأزرار القوائم
        const listTabs = document.querySelectorAll('[id^="list-tab-"]');
        listTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const listId = e.target.dataset.listId ? parseInt(e.target.dataset.listId) : null;
                this.currentListId = listId;
                console.log(`Switching to list ${listId || 'all'}`);
                this.loadStores(listId);

                // تحديث عنوان القائمة
                const listTitle = document.querySelector('.store-list-header h5');
                if (listTitle) {
                    listTitle.textContent = listId ? `قائمة المتاجر ${listId}` : 'جميع المتاجر';
                }
            });
        });
    }

    /**
     * Load stores from the server
     * @param {number|null} listId - Optional list ID to filter stores
     * @param {string|null} region - Optional region name to filter stores
     * @returns {Promise} A promise that resolves when stores are loaded
     */
    loadStores(listId = null, region = null) {
        console.log(`Loading stores from server for list ${listId || 'all'}, region: ${region || 'all'}...`);

        // بناء عنوان URL مع معلمات الاستعلام إذا لزم الأمر
        let url = '/api/stores';
        let params = [];

        if (listId !== null) {
            params.push(`list_id=${listId}`);
        }

        // إضافة معلمة المنطقة إلى عنوان URL
        this.currentRegion = region;

        if (params.length > 0) {
            url += '?' + params.join('&');
        }

        // تحديث حالة زر جميع القوائم
        const viewAllListsBtn = document.getElementById('view-all-lists');
        if (viewAllListsBtn) {
            if (listId === null) {
                // إذا كنا نعرض جميع القوائم، قم بتعطيل الزر
                viewAllListsBtn.classList.add('active');
                viewAllListsBtn.classList.remove('btn-outline-primary');
                viewAllListsBtn.classList.add('btn-primary');
            } else {
                // إذا كنا نعرض قائمة محددة، قم بتمكين الزر
                viewAllListsBtn.classList.remove('active');
                viewAllListsBtn.classList.add('btn-outline-primary');
                viewAllListsBtn.classList.remove('btn-primary');
            }
        }

        // إضافة مؤشر التحميل
        const storeListContainer = document.getElementById('storeList');
        if (storeListContainer) {
            storeListContainer.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                    <p class="mt-2 text-muted">جاري تحميل المتاجر...</p>
                </div>
            `;
        }

        // إرجاع وعد (Promise) ليتم استخدامه في الدوال الأخرى
        return fetch(url)
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Loaded stores:', data);
                // التحقق من بنية البيانات المستلمة
                this.stores = data.stores || data;

                // التأكد من وجود حاوية قائمة المتاجر
                this.storeListContainer = document.getElementById('storeList');
                console.log('Store list container (before render):', this.storeListContainer);

                // إضافة صنف الانتقال للحاوية
                if (this.storeListContainer) {
                    this.storeListContainer.classList.add('list-transition');

                    // إزالة صنف الانتقال بعد انتهاء التأثير
                    setTimeout(() => {
                        this.storeListContainer.classList.remove('list-transition');
                    }, 300);
                }

                // تصفية المتاجر حسب المنطقة إذا تم تحديدها
                if (this.currentRegion) {
                    this.stores = this.stores.filter(store => {
                        // استخدام مكتبة المناطق الليبية لتحديد المنطقة
                        if (typeof LibyaRegions !== 'undefined') {
                            const storeRegion = LibyaRegions.getRegionFromCoordinates(store.latitude, store.longitude);
                            return storeRegion === this.currentRegion;
                        }
                        return true;
                    });
                }

                // عرض المتاجر
                this.renderStores();

                // تحديث عنوان القائمة
                const listTitle = document.querySelector('.store-list-header h5');
                if (listTitle) {
                    let title = listId ? `قائمة المتاجر ${listId}` : 'جميع المتاجر';
                    if (this.currentRegion) {
                        title += ` - منطقة ${this.currentRegion}`;
                    }
                    listTitle.textContent = title;
                }

                // لا نقوم بالانتقال إلى تبويب القائمة تلقائيًا لجعل حركة القائمة مستقلة عن حقول الإدخال
                console.log('Stores loaded successfully, found', this.stores.length, 'stores');

                // إرجاع البيانات للاستخدام في الدوال الأخرى
                return this.stores;
            })
            .catch(error => {
                console.error('Error loading stores:', error);

                // عرض رسالة خطأ في حالة فشل التحميل
                if (this.storeListContainer) {
                    this.storeListContainer.innerHTML = `
                        <div class="text-center py-5">
                            <i class="fas fa-exclamation-triangle text-warning mb-3" style="font-size: 48px;"></i>
                            <p class="text-muted">حدث خطأ أثناء تحميل المتاجر. يرجى المحاولة مرة أخرى.</p>
                        </div>
                    `;
                }

                // إرجاع مصفوفة فارغة في حالة الخطأ
                return [];
            });
    }

    /**
     * Save stores to local storage
     */
    saveStores() {
        localStorage.setItem('stores', JSON.stringify(this.stores));
    }

    /**
     * Add a new store to the server
     * @param {Object} storeData - The store data to add
     */
    addStore(storeData) {
        // إنشاء كائن FormData لإرسال البيانات والملفات
        const formData = new FormData();

        // إضافة البيانات إلى النموذج
        formData.append('name', storeData.name);
        formData.append('phone', storeData.phone || '');
        formData.append('type', storeData.type || 'A');
        formData.append('address', storeData.address || '');
        formData.append('full_address', storeData.full_address || '');
        formData.append('city_name', storeData.city_name || '');
        formData.append('region_name', storeData.region_name || '');
        formData.append('city_id', storeData.city_id || '');
        formData.append('region_id', storeData.region_id || '');

        // التأكد من وجود إحداثيات الموقع
        const latitude = storeData.lat || storeData.latitude;
        const longitude = storeData.lng || storeData.longitude;

        if (!latitude || !longitude) {
            console.error('Missing location coordinates');
            // عرض رسالة خطأ واضحة
            this.showNotification('⚠️ يجب تحديد موقع المتجر على الخريطة قبل الإضافة', 'danger', 5000);

            // تسليط الضوء على الخريطة لجذب انتباه المستخدم
            const mapContainer = document.getElementById('map');
            if (mapContainer) {
                mapContainer.classList.add('map-highlight');
                setTimeout(() => {
                    mapContainer.classList.remove('map-highlight');
                }, 1500);

                // التبديل إلى تبويب الخريطة
                const mapTab = document.getElementById('map-tab');
                if (mapTab) {
                    mapTab.click();
                }
            }

            return Promise.reject(new Error('يجب تحديد موقع المتجر على الخريطة قبل الإضافة'));
        }

        formData.append('latitude', latitude);
        formData.append('longitude', longitude);

        // إضافة رقم القائمة إذا كان موجودًا
        if (storeData.list_id) {
            formData.append('list_id', storeData.list_id);
            console.log('Adding store to list:', storeData.list_id);
        }

        // إضافة الصورة إذا كانت موجودة
        if (storeData.imageFile) {
            formData.append('image', storeData.imageFile);
        }

        console.log('Adding store with data:', storeData);

        // طباعة محتويات FormData للتشخيص
        for (let pair of formData.entries()) {
            console.log(pair[0] + ': ' + pair[1]);
        }

        // إضافة رأس CSRF إذا كان موجودًا
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        const headers = {};
        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken;
        }

        // إرسال البيانات إلى الخادم
        return fetch('/api/stores', {
            method: 'POST',
            headers: headers,
            body: formData
        })
        .then(response => {
            console.log('Add store response:', response);
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('فشل في إضافة المتجر');
            }
        })
        .then(response => {
            console.log('Store added successfully:', response);
            // التحقق من بنية البيانات المستلمة
            if (response.error) {
                throw new Error(response.error);
            }

            // إضافة خاصية success للاستجابة للتأكد من أن العملية ناجحة
            if (!response.success && response.store_id) {
                response.success = true;
            }

            // إعادة تحميل المتاجر من الخادم مباشرة
            console.log('Reloading stores after adding new store');

            // إرجاع وعد جديد يتضمن تحميل المتاجر وإرجاع الاستجابة
            try {
                return this.loadStores().then(() => {
                    // إرجاع معرف المتجر الجديد
                    return response.store_id ? response : { ...response, success: true };
                });
            } catch (error) {
                console.error('Error in loadStores after adding store:', error);
                // إرجاع الاستجابة حتى في حالة فشل تحميل المتاجر
                return response.store_id ? response : { ...response, success: true };
            }
        })
        .catch(error => {
            console.error('Error adding store:', error);
            throw error;
        });
    }

    /**
     * Update an existing store
     * @param {string} storeId - The ID of the store to update
     * @param {Object} updatedData - The updated store data
     */
    updateStore(updatedData) {
        // التحقق من صلاحيات المستخدم
        const userRoleElement = document.querySelector('meta[name="user-role"]');
        const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

        // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه تحديث المتاجر
        if (userRole === 3) {
            return Promise.reject(new Error('ليس لديك صلاحية لتحديث المتاجر'));
        }

        // إنشاء كائن FormData لإرسال البيانات والملفات
        const formData = new FormData();

        // إضافة البيانات إلى النموذج
        formData.append('id', updatedData.id);
        formData.append('name', updatedData.name);
        formData.append('phone', updatedData.phone || '');
        formData.append('latitude', updatedData.lat || updatedData.latitude);
        formData.append('longitude', updatedData.lng || updatedData.longitude);
        formData.append('type', updatedData.type || 'A');
        formData.append('address', updatedData.address || '');
        formData.append('full_address', updatedData.full_address || '');
        formData.append('city_name', updatedData.city_name || '');
        formData.append('region_name', updatedData.region_name || '');
        formData.append('city_id', updatedData.city_id || '');
        formData.append('region_id', updatedData.region_id || '');

        // إضافة رقم القائمة إذا كان موجودًا
        if (updatedData.list_id) {
            formData.append('list_id', updatedData.list_id);
            console.log('Updating store list to:', updatedData.list_id);
        }

        // إضافة الصورة إذا كانت موجودة
        if (updatedData.imageFile) {
            formData.append('image', updatedData.imageFile);
        }

        console.log('Sending updated store data to server:', formData);

        // إضافة رأس CSRF إذا كان موجودًا
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        const headers = {};
        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken;
        }

        // إرسال البيانات إلى الخادم
        return fetch('/api/stores', {
            method: 'PUT',
            headers: headers,
            body: formData
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Failed to update store');
            }
        })
        .then(response => {
            console.log('Store updated successfully:', response);

            // التحقق من بنية البيانات المستلمة
            if (response.error) {
                throw new Error(response.error);
            }

            // الحصول على المتجر المحدث
            const updatedStore = response.success ? updatedData : response;

            // تحديث المتجر في المصفوفة المحلية
            const index = this.stores.findIndex(store => store.id === updatedStore.id);
            if (index !== -1) {
                this.stores[index] = updatedStore;

                // عرض المتاجر بدون إعادة تحميل من الخادم
                this.renderStores();
            }

            // إعادة تحميل المتاجر من الخادم بعد فترة قصيرة
            setTimeout(() => {
                try {
                    this.loadStores().catch(error => {
                        console.error('Error reloading stores after update:', error);
                    });
                } catch (error) {
                    console.error('Error calling loadStores after update:', error);
                }
            }, 500);

            return updatedStore;
        })
        .catch(error => {
            console.error('Error updating store:', error);
            throw error;
        });
    }

    /**
     * Delete a store
     * @param {string} storeId - The ID of the store to delete
     * @returns {Promise} A promise that resolves when the store is deleted
     */
    deleteStore(storeId) {
        // إضافة رأس CSRF إذا كان موجودًا
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        const headers = {};
        if (csrfToken) {
            headers['X-CSRFToken'] = csrfToken;
        }

        // إرسال طلب حذف المتجر إلى الخادم
        return fetch(`/api/stores?id=${storeId}`, {
            method: 'DELETE',
            headers: headers
        })
        .then(response => {
            if (response.ok) {
                return response.json();
            } else {
                throw new Error('Failed to delete store');
            }
        })
        .then(response => {
            console.log('Store deleted successfully:', response);

            // التحقق من بنية البيانات المستلمة
            if (response.error) {
                throw new Error(response.error);
            }

            // تحديث القائمة المحلية بعد الحذف
            this.stores = this.stores.filter(store => store.id !== storeId);

            // عرض المتاجر بدون إعادة تحميل من الخادم
            this.renderStores();

            return response;
        })
        .catch(error => {
            console.error('Error deleting store:', error);
            throw error;
        });
    }

    /**
     * Render all stores on the map and in the list
     */
    renderStores() {
        console.log('Rendering stores:', this.stores);

        // التحقق من وجود الخريطة قبل محاولة استخدامها
        if (this.map && typeof this.map.clearMarkers === 'function') {
            // Clear existing markers
            this.map.clearMarkers();

            // Add markers for each store
            this.stores.forEach(store => {
                console.log('Adding marker for store:', store);
                if (typeof this.map.addStoreMarker === 'function') {
                    this.map.addStoreMarker(store, (clickedStore) => {
                        this.selectStore(clickedStore);
                    });
                } else {
                    console.warn('addStoreMarker method not available on map object');
                }
            });
        } else {
            console.warn('Map object not available or clearMarkers method not found. Skipping map rendering.');

            // محاولة الحصول على الخريطة من النافذة العامة
            if (window.storeMap && window.storeMap.clearMarkers) {
                this.map = window.storeMap;
                console.log('✅ تم ربط StoreManager بالخريطة من النافذة العامة');

                // إعادة المحاولة
                this.map.clearMarkers();
                this.stores.forEach(store => {
                    console.log('Adding marker for store:', store);
                    this.map.addStoreMarker(store, (clickedStore) => {
                        this.selectStore(clickedStore);
                    });
                });
            } else {
                console.warn('⚠️ لا يمكن العثور على الخريطة. سيتم عرض قائمة المتاجر فقط.');
            }
        }

        // Render store list
        this.renderStoreList();
    }

    /**
     * Render store markers on the map only
     */
    renderStoreMarkers() {
        console.log('Rendering store markers on map:', this.stores.length, 'stores');

        // التحقق من وجود الخريطة قبل محاولة استخدامها
        if (this.map && typeof this.map.clearMarkers === 'function') {
            // Clear existing markers
            this.map.clearMarkers();

            // Add markers for each store
            this.stores.forEach(store => {
                console.log('Adding marker for store:', store);
                if (typeof this.map.addStoreMarker === 'function') {
                    this.map.addStoreMarker(store, (clickedStore) => {
                        this.selectStore(clickedStore);
                    });
                } else {
                    console.warn('addStoreMarker method not available on map object');
                }
            });

            console.log('✅ تم عرض', this.stores.length, 'متجر على الخريطة');
        } else {
            console.warn('Map object not available for rendering markers');

            // محاولة الحصول على الخريطة من النافذة العامة
            if (window.storeMap && window.storeMap.clearMarkers) {
                this.map = window.storeMap;
                console.log('✅ تم ربط StoreManager بالخريطة من النافذة العامة');

                // إعادة المحاولة
                this.map.clearMarkers();
                this.stores.forEach(store => {
                    console.log('Adding marker for store:', store);
                    this.map.addStoreMarker(store, (clickedStore) => {
                        this.selectStore(clickedStore);
                    });
                });

                console.log('✅ تم عرض', this.stores.length, 'متجر على الخريطة (من النافذة العامة)');
            } else {
                console.warn('⚠️ لا يمكن العثور على الخريطة لعرض المتاجر');
            }
        }
    }

    /**
     * Render the store list in the UI
     */
    renderStoreList() {
        console.log('Rendering store list, container:', this.storeListContainer);

        // التأكد من وجود حاوية قائمة المتاجر
        if (!this.storeListContainer) {
            console.error('Store list container not found! Trying to find it again...');
            this.storeListContainer = document.getElementById('storeList');

            // إذا لم يتم العثور على الحاوية، نحاول مرة أخرى بعد قليل
            if (!this.storeListContainer) {
                console.error('Still could not find store list container. Will retry later.');
                setTimeout(() => {
                    this.storeListContainer = document.getElementById('storeList');
                    console.log('Store list container (retry):', this.storeListContainer);
                    if (this.storeListContainer) {
                        this.renderStoreList();
                    }
                }, 500);
                return;
            }
        }

        // تحديث عدد المتاجر
        const storeCountEl = document.getElementById('storeCount');
        if (storeCountEl) {
            storeCountEl.textContent = this.stores.length;
        }

        this.storeListContainer.innerHTML = '';

        if (this.stores.length === 0) {
            console.log('No stores to display');
            this.storeListContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-store mb-3" style="font-size: 48px; color: var(--bs-secondary);"></i>
                    <p class="text-muted">لم تتم إضافة أي متاجر بعد. انقر على الخريطة لإضافة أول متجر!</p>
                </div>
            `;
            return;
        }

        console.log('Displaying', this.stores.length, 'stores');

        this.stores.forEach(store => {
            console.log('Creating element for store:', store);
            const storeEl = document.createElement('div');
            storeEl.className = 'card mb-3 store-card';
            storeEl.dataset.storeId = store.id;

            // التأكد من وجود الإحداثيات
            const lat = store.latitude || store.lat;
            const lng = store.longitude || store.lng;
            const coordsText = (lat && lng) ? `${parseFloat(lat).toFixed(6)}, ${parseFloat(lng).toFixed(6)}` : 'لا توجد إحداثيات';

            storeEl.innerHTML = `
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="d-flex align-items-start">
                            <div class="form-check ms-2">
                                <input class="form-check-input store-checkbox" type="checkbox" value="${store.id}" id="store-check-${store.id}">
                                <label class="form-check-label" for="store-check-${store.id}">
                                    <span class="visually-hidden">تحديد المتجر</span>
                                </label>
                            </div>
                            <div>
                                <h5 class="card-title">${store.name || 'متجر بدون اسم'}</h5>
                                <h6 class="card-subtitle mb-2 text-muted">${store.phone || 'لا يوجد رقم هاتف'}</h6>
                                <div class="d-flex flex-wrap gap-1 mb-2">
                                    <span class="badge bg-info">القائمة ${store.list_id || 1}</span>
                                    <span class="badge bg-primary">النوع ${store.type || 'A'}</span>
                                </div>
                                ${(store.city_name || store.region_name) ? `
                                <div class="store-location-info mb-2">
                                    <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                    <span class="text-muted small">
                                        ${store.city_name && store.region_name ? `${store.city_name} - ${store.region_name}` :
                                          store.city_name ? store.city_name :
                                          store.region_name ? store.region_name : ''}
                                    </span>
                                </div>
                                ` : `
                                <div class="store-location-info mb-2">
                                    <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                    <span class="small" style="color: #dc3545; font-weight: 500;">المنطقة غير محددة</span>
                                </div>
                                `}
                                ${store.address ? `
                                <div class="store-address-info mb-2">
                                    <i class="fas fa-info-circle text-muted me-1"></i>
                                    <span class="text-muted small">${store.address}</span>
                                </div>
                                ` : ''}
                                <p class="card-text small">
                                    <i class="fas fa-globe"></i>
                                    ${coordsText}
                                </p>
                            </div>
                        </div>
                        ${store.image_path ? `
                            <div class="store-image">
                                <img src="/${store.image_path}" alt="${store.name || 'متجر'}" class="rounded shadow-sm store-image-thumbnail" style="width: 70px; height: 70px; object-fit: cover; border: 2px solid var(--bs-primary); transition: all 0.3s ease; cursor: pointer;" data-image-src="/${store.image_path}" data-store-name="${store.name || 'متجر'}" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1.0)'">
                            </div>
                        ` : (store.imageUrl ? `
                            <div class="store-image">
                                <img src="${store.imageUrl}" alt="${store.name || 'متجر'}" class="rounded shadow-sm store-image-thumbnail" style="width: 70px; height: 70px; object-fit: cover; border: 2px solid var(--bs-primary); transition: all 0.3s ease; cursor: pointer;" data-image-src="${store.imageUrl}" data-store-name="${store.name || 'متجر'}" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1.0)'">
                            </div>
                        ` : `
                            <div class="store-image">
                                <div class="rounded bg-secondary d-flex align-items-center justify-content-center" style="width: 70px; height: 70px;">
                                    <i class="fas fa-store text-light" style="font-size: 24px;"></i>
                                </div>
                            </div>
                        `)}
                    </div>
                    <div class="btn-group w-100 mt-2" role="group">
                        <button class="btn btn-sm btn-outline-primary locate-store" data-store-id="${store.id}">
                            <i class="fas fa-map-marked-alt"></i> تحديد الموقع
                        </button>
                        <button class="btn btn-sm btn-outline-secondary edit-store" data-store-id="${store.id}">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-sm btn-outline-danger delete-store" data-store-id="${store.id}">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                        <a href="#" class="btn btn-sm btn-outline-success share-whatsapp" data-store-id="${store.id}">
                            <i class="fab fa-whatsapp"></i> مشاركة
                        </a>
                    </div>
                </div>
            `;

            // إضافة مستمعي الأحداث بطريقة أكثر موثوقية
            const storeIdStr = String(store.id);

            // إضافة مستمع حدث لخانة الاختيار
            const checkbox = storeEl.querySelector('.store-checkbox');
            if (checkbox) {
                checkbox.addEventListener('change', () => {
                    // تحديث المظهر المرئي للبطاقة عند تحديدها
                    if (checkbox.checked) {
                        storeEl.classList.add('selected-store');
                    } else {
                        storeEl.classList.remove('selected-store');
                    }

                    // تحديث عدد المتاجر المحددة
                    this.updateSelectedStoresCount();
                });
            }

            // مستمع حدث النقر على الصورة
            const storeImage = storeEl.querySelector('.store-image-thumbnail');
            if (storeImage) {
                storeImage.onclick = (e) => {
                    e.preventDefault();
                    const imageSrc = storeImage.getAttribute('data-image-src');
                    const storeName = storeImage.getAttribute('data-store-name');
                    console.log('Image clicked for store:', storeName);

                    // فتح النافذة المنبثقة وعرض الصورة
                    const modalImage = document.getElementById('modalImage');
                    const modalTitle = document.getElementById('imageModalLabel');

                    if (modalImage && modalTitle) {
                        modalImage.src = imageSrc;
                        modalTitle.textContent = `صورة ${storeName}`;

                        // فتح النافذة المنبثقة
                        const imageModal = new bootstrap.Modal(document.getElementById('imageModal'));
                        imageModal.show();
                    }
                };
            }

            // زر تحديد الموقع
            const locateBtn = storeEl.querySelector('.locate-store');
            if (locateBtn) {
                locateBtn.onclick = (e) => {
                    e.preventDefault();
                    console.log('Locate button clicked for store ID:', storeIdStr);
                    this.locateStore(storeIdStr);
                };
            }

            // زر التعديل
            const editBtn = storeEl.querySelector('.edit-store');
            if (editBtn) {
                editBtn.onclick = (e) => {
                    e.preventDefault();
                    console.log('Edit button clicked for store ID:', storeIdStr);
                    this.editStore(storeIdStr);
                };
            }

            // زر الحذف
            const deleteBtn = storeEl.querySelector('.delete-store');
            if (deleteBtn) {
                deleteBtn.onclick = (e) => {
                    e.preventDefault();
                    console.log('Delete button clicked for store ID:', storeIdStr);
                    this.confirmDeleteStore(storeIdStr);
                };
            }

            // زر المشاركة
            const shareBtn = storeEl.querySelector('.share-whatsapp');
            if (shareBtn) {
                shareBtn.onclick = (e) => {
                    e.preventDefault();
                    console.log('Share button clicked for store ID:', storeIdStr);
                    this.shareStoreOnWhatsApp(storeIdStr);
                };
            }

            this.storeListContainer.appendChild(storeEl);
        });

        // تحديث عداد المتاجر المحددة
        this.updateSelectedStoresCount();

        // إضافة مستمعي الأحداث لأزرار المشاركة في النوافذ المنبثقة
        const popupShareButtons = document.querySelectorAll('.leaflet-popup-content .share-whatsapp');
        popupShareButtons.forEach(button => {
            if (button.dataset.hasListener !== 'true') {
                button.onclick = (e) => {
                    e.preventDefault();
                    const storeId = button.dataset.storeId;
                    console.log('Popup share button clicked for store ID:', storeId);
                    this.shareStoreOnWhatsApp(storeId);
                };
                button.dataset.hasListener = 'true';
            }
        });

        console.log('Store list rendered with', this.stores.length, 'stores');
    }

    /**
     * Select a store and populate the form for editing
     * @param {Object} store - The store to select
     */
    selectStore(store) {
        // Populate the form with store data
        document.getElementById('storeId').value = store.id;
        document.getElementById('storeName').value = store.name;
        document.getElementById('storePhone').value = store.phone || '';

        // تعيين نوع المتجر والعنوان
        const storeTypeSelect = document.getElementById('storeType');
        if (storeTypeSelect) {
            storeTypeSelect.value = store.type || 'A';
        }

        const storeAddressInput = document.getElementById('storeAddress');
        if (storeAddressInput) {
            storeAddressInput.value = store.address || '';
        }

        // تحديث حقل عنوان المتجر
        const storeFullAddressInput = document.getElementById('storeFullAddress');
        if (storeFullAddressInput) {
            storeFullAddressInput.value = store.full_address || '';
        }

        // تعيين المدينة والمنطقة إذا كانت متوفرة
        const citySelect = document.getElementById('citySelect');
        const districtSelect = document.getElementById('districtSelect');

        if (citySelect && store.city_id) {
            citySelect.value = store.city_id;
            // تفعيل قائمة المناطق
            if (districtSelect) {
                districtSelect.disabled = false;
                // إخفاء جميع مجموعات المناطق
                const optgroups = districtSelect.querySelectorAll('optgroup');
                optgroups.forEach(optgroup => {
                    optgroup.style.display = 'none';
                });

                // إظهار مجموعة المناطق للمدينة المختارة
                let cityClassName = '';
                switch (store.city_name) {
                    case 'طرابلس':
                        cityClassName = 'tripoli';
                        break;
                    case 'بنغازي':
                        cityClassName = 'benghazi';
                        break;
                    case 'مصراتة':
                        cityClassName = 'misrata';
                        break;
                    default:
                        cityClassName = (store.city_name || '').toLowerCase().replace(/\s+/g, '-');
                }

                const cityDistricts = districtSelect.querySelector(`.${cityClassName}-districts`);
                if (cityDistricts) {
                    cityDistricts.style.display = 'block';
                }
            }
        }

        if (districtSelect && store.region_id) {
            districtSelect.value = store.region_id;
        }

        // تحديث حقل عنوان المتجر الكامل
        if (storeFullAddressInput && (store.city_name || store.region_name)) {
            if (store.city_name && store.region_name) {
                storeFullAddressInput.value = `${store.city_name} - ${store.region_name}`;
            } else if (store.city_name) {
                storeFullAddressInput.value = store.city_name;
            } else if (store.region_name) {
                storeFullAddressInput.value = store.region_name;
            }
        }

        // معالجة الصورة
        const imagePreview = document.getElementById('imagePreview');
        const imagePreviewContainer = document.getElementById('imagePreviewContainer');

        // التحقق من وجود صورة
        if (store.image_path) {
            // عرض الصورة من قاعدة البيانات
            imagePreview.src = `/${store.image_path}`;
            imagePreviewContainer.style.display = 'block';
        } else if (store.imageUrl) {
            // عرض الصورة من URL مباشر
            imagePreview.src = store.imageUrl;
            imagePreviewContainer.style.display = 'block';
        } else {
            // لا توجد صورة
            imagePreviewContainer.style.display = 'none';
        }

        // تعيين الموقع المحدد على الخريطة
        const lat = store.latitude || store.lat;
        const lng = store.longitude || store.lng;
        this.map.setSelectedLocation(L.latLng(lat, lng));

        // تحديث واجهة المستخدم
        document.getElementById('formTitle').textContent = 'تعديل المتجر';
        document.getElementById('submitStore').innerHTML = '<i class="fas fa-save me-1"></i> تحديث المتجر';

        // إظهار زر التعديل فقط عند تعديل المتجر بالكامل وليس فقط عند تغيير الاسم
        // نتحقق من أن المتجر يحتوي على معرف وموقع
        if (store && store.id && (store.latitude || store.lat)) {
            const editButton = document.getElementById('editStore');
            if (editButton) {
                editButton.classList.remove('d-none');
            }
        }

        // تحديث قائمة المتاجر المحددة
        const storeListSelect = document.getElementById('storeListSelect');
        if (storeListSelect && store.list_id) {
            storeListSelect.value = store.list_id;
        }
    }

    /**
     * Locate a store on the map
     * @param {string} storeId - The ID of the store to locate
     */
    locateStore(storeId) {
        const store = this.stores.find(s => s.id === storeId);

        if (store) {
            // Switch to the map tab first
            const mapTab = document.getElementById('map-tab');
            if (mapTab) {
                mapTab.click();
            }

            // Fly to store - ستتحقق الدالة من وجود موقع وترجع false إذا لم يكن هناك موقع
            const success = this.map.flyToStore(store);

            // إذا لم يكن للمتجر موقع، عرض رسالة تنبيه
            if (!success) {
                showAlert('⚠️ لا يوجد موقع مضاف لهذا المتجر', 'warning');
            }
        }
    }

    /**
     * Edit a store
     * @param {string} storeId - The ID of the store to edit
     */
    editStore(storeId) {
        console.log('Editing store with ID:', storeId);

        // التحقق من صلاحيات المستخدم
        const userRoleElement = document.querySelector('meta[name="user-role"]');
        const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

        // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه تعديل المتاجر
        if (userRole === 3) {
            return;
        }

        try {
            // استخدام الدالة الجديدة للبحث عن المتجر
            const store = this.findStoreById(storeId);
            console.log('Found store for editing:', store);

            if (store) {
                // التبديل إلى تبويب الخريطة لرؤية الموقع
                const mapTab = document.getElementById('map-tab');
                if (mapTab) {
                    mapTab.click();
                }

                // تأخير قليل للسماح بتحميل الخريطة
                setTimeout(() => {
                    // تحديد المتجر وملء النموذج
                    this.selectStore(store);

                    // تحديث واجهة الموبايل
                    this.updateMobileFormForEdit(store);

                    // التمرير إلى النموذج
                    setTimeout(() => {
                        const storeForm = document.getElementById('storeForm');
                        if (storeForm) {
                            storeForm.scrollIntoView({ behavior: 'smooth' });
                        } else {
                            console.error('Store form element not found');
                        }
                    }, 200);
                }, 300);
            } else {
                console.error('Store not found with ID:', storeId);
                alert('لم يتم العثور على المتجر');
            }
        } catch (error) {
            console.error('Error editing store:', error);
            alert('حدث خطأ أثناء محاولة تعديل المتجر');
        }
    }

    /**
     * Confirm deletion of a store
     * @param {string} storeId - The ID of the store to delete
     */
    confirmDeleteStore(storeId) {
        // التحقق من صلاحيات المستخدم
        const userRoleElement = document.querySelector('meta[name="user-role"]');
        const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

        // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه حذف المتاجر
        if (userRole === 3) {
            return;
        }

        const confirmed = confirm('هل أنت متأكد من رغبتك في حذف هذا المتجر؟');

        if (confirmed) {
            this.deleteStore(storeId);
            showAlert('تم حذف المتجر بنجاح', 'success');
        }
    }

    /**
     * Share a store location via WhatsApp
     * @param {string} storeId - The ID of the store to share
     */
    shareStoreOnWhatsApp(storeId) {
        console.log('Sharing single store with ID:', storeId);

        try {
            // استخدام الدالة الجديدة للبحث عن المتجر
            const store = this.findStoreById(storeId);
            console.log('Found store for sharing:', store);

            if (store) {
                // استخدام الدالة الجديدة لمشاركة المتجر عبر واتساب
                const success = this.map.shareStoreViaWhatsApp(store);

                if (success) {
                    console.log('Store shared successfully via WhatsApp');
                } else {
                    console.error('Failed to share store via WhatsApp');
                    alert('فشل فتح واتساب. يرجى المحاولة مرة أخرى.');
                }
            } else {
                console.error('Store not found with ID:', storeId);
                alert('لم يتم العثور على المتجر');
            }
        } catch (error) {
            console.error('Error sharing store:', error);
            alert('حدث خطأ أثناء محاولة مشاركة المتجر');
        }
    }

    /**
     * Share multiple stores via WhatsApp
     * @param {Array} storeIds - Array of store IDs to share
     */
    shareMultipleStoresOnWhatsApp(storeIds) {
        try {
            // التحقق من وجود متاجر محددة
            if (!storeIds || storeIds.length === 0) {
                // التحقق من وجود متاجر محددة باستخدام خانات الاختيار
                const checkedBoxes = document.querySelectorAll('.store-checkbox:checked');
                if (checkedBoxes.length > 0) {
                    // إذا كانت هناك خانات اختيار محددة، استخدمها مباشرة
                    storeIds = Array.from(checkedBoxes).map(cb => cb.value);
                    console.log('Using checked boxes directly:', storeIds);
                } else {
                    alert('يرجى تحديد متجر واحد على الأقل');
                    return;
                }
            }

            // الآن لدينا مجموعة من معرفات المتاجر
            console.log('Sharing stores with IDs:', storeIds);

            // جمع معلومات المتاجر للمشاركة
            const storesToShare = [];

            // التحقق من كل متجر محدد
            if (Array.isArray(storeIds)) {
                for (const storeId of storeIds) {
                    // البحث عن المتجر في قائمة المتاجر
                    const store = this.findStoreById(storeId);
                    if (store) {
                        storesToShare.push(store);
                    }
                }
            }

            if (storesToShare.length === 0) {
                alert('لم يتم العثور على المتاجر المحددة');
                return;
            }

            // إنشاء نص المشاركة
            let shareText = 'مواقع المتاجر:\n\n';

            storesToShare.forEach((store, index) => {
                const lat = store.latitude || store.lat;
                const lng = store.longitude || store.lng;

                shareText += `${index + 1}. ${store.name || 'متجر بدون اسم'}\n`;

                // إضافة عنوان المتجر
                let storeAddress = '';
                if (store.city_name && store.region_name) {
                    storeAddress = `${store.city_name} - ${store.region_name}`;
                } else if (store.address) {
                    storeAddress = store.address;
                }

                if (storeAddress) {
                    shareText += `العنوان: ${storeAddress}\n`;
                }

                if (store.phone) shareText += `الهاتف: ${store.phone}\n`;
                if (lat && lng) shareText += `الموقع: https://www.google.com/maps?q=${lat},${lng}\n`;
                shareText += '\n';
            });

            // إنشاء رابط واتساب
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(shareText)}`;
            window.open(whatsappUrl, '_blank');

        } catch (error) {
            console.error('Error sharing stores:', error);
            alert('حدث خطأ في مشاركة المتاجر المحددة');
        }
    }

    /**
     * حذف مجموعة من المتاجر دفعة واحدة
     */
    batchDeleteStores() {
        // التحقق من صلاحيات المستخدم
        const userRoleElement = document.querySelector('meta[name="user-role"]');
        const userRole = userRoleElement ? parseInt(userRoleElement.getAttribute('content')) : null;

        // إذا كان المستخدم زائراً (الدور 3)، لا يمكنه حذف المتاجر
        if (userRole === 3) {
            return;
        }

        // الحصول على المتاجر المحددة
        const selectedStoreIds = this.getSelectedStoreIds();

        if (selectedStoreIds.length === 0) {
            showAlert('يرجى تحديد متجر واحد على الأقل للحذف', 'warning');
            return;
        }

        // عرض عدد المتاجر المحددة في نافذة التأكيد
        const deleteConfirmationCount = document.getElementById('deleteConfirmationCount');
        if (deleteConfirmationCount) {
            deleteConfirmationCount.textContent = `عدد المتاجر المحددة للحذف: ${selectedStoreIds.length}`;
        }

        // عرض نافذة التأكيد
        const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmationModal'));
        deleteModal.show();

        // إضافة مستمع حدث لزر التأكيد
        const confirmDeleteBtn = document.getElementById('confirmDelete');
        if (confirmDeleteBtn) {
            // إزالة مستمعات الأحداث السابقة لتجنب التكرار
            const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
            confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);

            newConfirmBtn.addEventListener('click', async () => {
                try {
                    // حذف المتاجر المحددة واحدًا تلو الآخر
                    for (const storeId of selectedStoreIds) {
                        await this.deleteStore(storeId);
                    }

                    // إغلاق النافذة المنبثقة
                    deleteModal.hide();

                    // عرض رسالة نجاح
                    showAlert(`تم حذف ${selectedStoreIds.length} متجر بنجاح`, 'success');

                    // إعادة تحميل المتاجر
                    this.loadStores();

                } catch (error) {
                    console.error('Error deleting stores:', error);
                    showAlert('حدث خطأ أثناء حذف المتاجر', 'danger');
                }
            });
        }

        // تم إزالة استدعاء دالة مشاركة واتساب من هنا لمنع التداخل بين الأزرار
    }

    /**
     * مشاركة المتاجر المحددة على واتساب
     */
    shareSelectedStoresOnWhatsApp() {
        // الحصول على المتاجر المحددة
        const selectedStoreIds = this.getSelectedStoreIds();
        console.log('Selected store IDs for sharing:', selectedStoreIds);
        console.log('Available stores:', this.stores);

        try {
            // تجميع المتاجر المحددة
            const storesToShare = [];

            // التحقق من كل متجر محدد
            for (const storeId of selectedStoreIds) {
                // البحث عن المتجر في قائمة المتاجر
                const store = this.findStoreById(storeId);
                if (store) {
                    storesToShare.push(store);
                    console.log('Added store to share:', store.name);
                }
            }

            console.log('Final stores to share:', storesToShare);

            if (storesToShare.length > 0) {
                // إنشاء رسالة للمشاركة
                let message = 'إليك المتاجر التي اخترتها:\n\n';

                storesToShare.forEach((store, index) => {
                    message += `${index + 1}. ${store.name}\n`;

                    // إضافة عنوان المتجر
                    let storeAddress = '';
                    if (store.city_name && store.region_name) {
                        storeAddress = `${store.city_name} - ${store.region_name}`;
                    } else if (store.address) {
                        storeAddress = store.address;
                    }

                    if (storeAddress) {
                        message += `   العنوان: ${storeAddress}\n`;
                    }

                    if (store.phone) {
                        message += `   الهاتف: ${store.phone}\n`;
                    }
                    const lat = store.latitude || store.lat;
                    const lng = store.longitude || store.lng;
                    message += `   الموقع: https://www.google.com/maps?q=${lat},${lng}\n\n`;
                });

                // استخدام الدالة الجديدة لفتح واتساب مباشرة
                const success = this.map.openWhatsApp(message);

                if (success) {
                    console.log('Multiple stores shared successfully via WhatsApp');
                    setTimeout(() => {
                        // إظهار رسالة للمستخدم
                        alert('تم فتح رابط واتساب لمشاركة ' + storesToShare.length + ' متجر');
                    }, 500);
                } else {
                    console.error('Failed to open WhatsApp for multiple stores');
                    alert('فشل فتح واتساب. يرجى المحاولة مرة أخرى.');
                }
            } else {
                alert('لم يتم العثور على المتاجر المحددة');
            }
        } catch (error) {
            console.error('Error sharing stores:', error);
            alert('حدث خطأ في مشاركة المتاجر المحددة');
        }
    }

    /**
     * Search stores by name or phone
     * @param {string} query - The search query
     */
    searchStores(query) {
        console.log('Searching stores with query:', query);

        // التبديل إلى تبويب قائمة المتاجر عند البحث
        if (query && query.trim() !== '' && document.getElementById('list-dropdown-menu')) {
            // النقر على زر جميع القوائم للبحث في جميع المتاجر
            const allListsButton = document.getElementById('list-tab-all');
            if (allListsButton && this.currentListId !== null) {
                allListsButton.click();
                return; // سيتم استدعاء البحث مرة أخرى بعد تحميل جميع المتاجر
            }
        }

        // إذا كان البحث يتضمن اسم منطقة، قم بتصفية المتاجر حسب المنطقة
        if (query && query.trim() !== '' && typeof LibyaRegions !== 'undefined') {
            const regions = LibyaRegions.getAllRegions();
            const matchedRegion = regions.find(region =>
                query.toLowerCase().includes(region.toLowerCase())
            );

            if (matchedRegion) {
                console.log(`Found region match: ${matchedRegion}`);
                // إعادة تحميل المتاجر مع تصفية حسب المنطقة
                this.loadStores(this.currentListId, matchedRegion);
                return;
            }
        }

        // التحقق من وجود بطاقات المتاجر في الصفحة
        const storeCards = document.querySelectorAll('.store-card');

        // إذا كان البحث فارغًا، عرض جميع المتاجر
        if (!query || query.trim() === '') {
            // إظهار جميع البطاقات وإزالة التمييز
            storeCards.forEach(card => {
                card.style.display = 'block';

                // إزالة التمييز من النص
                const nameElement = card.querySelector('.card-title');
                const phoneElement = card.querySelector('.card-subtitle');

                if (nameElement) {
                    const store = this.findStoreById(card.dataset.storeId);
                    if (store) {
                        nameElement.textContent = store.name || 'متجر بدون اسم';
                    }
                }

                if (phoneElement) {
                    const store = this.findStoreById(card.dataset.storeId);
                    if (store) {
                        phoneElement.textContent = store.phone || 'لا يوجد رقم هاتف';
                    }
                }
            });

            // تحديث عدد المتاجر المعروضة
            const storeCountEl = document.getElementById('storeCount');
            if (storeCountEl) {
                storeCountEl.textContent = storeCards.length;
            }

            // إزالة رسالة عدم وجود نتائج إذا كانت موجودة
            const noResultsMsg = document.getElementById('no-results-message');
            if (noResultsMsg) {
                noResultsMsg.remove();
            }

            return;
        }

        // تنفيذ البحث المحلي
        const normalizedQuery = query.toLowerCase().trim();
        const queryWords = normalizedQuery.split(/\s+/).filter(word => word.length > 0);

        let visibleCount = 0;

        // البحث في بطاقات المتاجر الموجودة
        storeCards.forEach(card => {
            const storeId = card.dataset.storeId;
            const store = this.findStoreById(storeId);

            if (store) {
                const storeName = (store.name || '').toLowerCase();
                const storePhone = (store.phone || '').toLowerCase();

                // التحقق من تطابق جميع كلمات البحث مع اسم المتجر أو رقم الهاتف
                const allWordsMatch = queryWords.every(word =>
                    storeName.includes(word) || storePhone.includes(word)
                );

                // إظهار أو إخفاء البطاقة بناءً على نتيجة البحث
                card.style.display = allWordsMatch ? 'block' : 'none';

                if (allWordsMatch) {
                    visibleCount++;

                    // تمييز كلمات البحث في النص
                    const nameElement = card.querySelector('.card-title');
                    const phoneElement = card.querySelector('.card-subtitle');

                    if (nameElement && store.name) {
                        let nameText = store.name;
                        queryWords.forEach(word => {
                            const regex = new RegExp(word, 'gi');
                            nameText = nameText.replace(regex, match => `<mark>${match}</mark>`);
                        });
                        nameElement.innerHTML = nameText;
                    }

                    if (phoneElement && store.phone) {
                        let phoneText = store.phone;
                        queryWords.forEach(word => {
                            const regex = new RegExp(word, 'gi');
                            phoneText = phoneText.replace(regex, match => `<mark>${match}</mark>`);
                        });
                        phoneElement.innerHTML = phoneText;
                    }
                }
            }
        });

        console.log('Search results:', visibleCount, 'stores found');

        // تحديث عدد المتاجر المعروضة
        const storeCountEl = document.getElementById('storeCount');
        if (storeCountEl) {
            storeCountEl.textContent = visibleCount;
        }

        // إظهار رسالة إذا لم يتم العثور على نتائج
        if (visibleCount === 0) {
            // إذا لم توجد نتائج بحث، أضف رسالة
            if (!document.getElementById('no-results-message')) {
                const noResultsMsg = document.createElement('div');
                noResultsMsg.id = 'no-results-message';
                noResultsMsg.className = 'alert alert-info mt-3';
                noResultsMsg.innerHTML = `<i class="fas fa-search me-2"></i> لا توجد نتائج تطابق "${query}"`;
                this.storeListContainer.prepend(noResultsMsg);
            } else {
                document.getElementById('no-results-message').innerHTML =
                    `<i class="fas fa-search me-2"></i> لا توجد نتائج تطابق "${query}"`;
            }
        } else {
            // إزالة رسالة عدم وجود نتائج إذا كانت موجودة
            const noResultsMsg = document.getElementById('no-results-message');
            if (noResultsMsg) {
                noResultsMsg.remove();
            }
        }
    }

    /**
     * Update mobile form for editing a store
     * @param {Object} store - The store to edit
     */
    updateMobileFormForEdit(store) {
        // تحديث نموذج الموبايل الأول
        const mobileStoreId = document.getElementById('mobileStoreId');
        const mobileStoreName = document.getElementById('mobileStoreName');
        const mobileStorePhone = document.getElementById('mobileStorePhone');
        const mobileFormTitle = document.getElementById('mobileFormTitle');
        const mobileSubmitStore = document.getElementById('mobileSubmitStore');
        const mobileEditStore = document.getElementById('mobileEditStore');
        const mobileClearForm = document.getElementById('mobileClearForm');

        if (mobileStoreId) mobileStoreId.value = store.id;
        if (mobileStoreName) mobileStoreName.value = store.name;
        if (mobileStorePhone) mobileStorePhone.value = store.phone || '';
        if (mobileFormTitle) mobileFormTitle.textContent = 'تعديل المتجر';
        if (mobileSubmitStore) mobileSubmitStore.innerHTML = '<i class="fas fa-save me-1"></i> تحديث المتجر';

        // إظهار زر التعديل فقط عند تعديل المتجر بالكامل وليس فقط عند تغيير الاسم
        if (store && store.id && (store.latitude || store.lat)) {
            if (mobileEditStore) mobileEditStore.classList.remove('d-none');
            if (mobileClearForm) mobileClearForm.classList.add('d-none');
        }

        // تحديث نموذج الموبايل الثاني
        const mobileStoreId2 = document.getElementById('mobile-store-id');
        const mobileStoreName2 = document.getElementById('mobile-store-name');
        const mobileStorePhone2 = document.getElementById('mobile-store-phone');
        const mobileSubmitStore2 = document.getElementById('mobile-submit-store');
        const mobileEditStore2 = document.getElementById('mobile-edit-store');
        const mobileClearForm2 = document.getElementById('mobile-clear-form');

        if (mobileStoreId2) mobileStoreId2.value = store.id;
        if (mobileStoreName2) mobileStoreName2.value = store.name;
        if (mobileStorePhone2) mobileStorePhone2.value = store.phone || '';
        if (mobileSubmitStore2) mobileSubmitStore2.innerHTML = '<i class="fas fa-save me-1"></i> تحديث المتجر';

        // إظهار زر التعديل فقط عند تعديل المتجر بالكامل وليس فقط عند تغيير الاسم
        if (store && store.id && (store.latitude || store.lat)) {
            if (mobileEditStore2) mobileEditStore2.classList.remove('d-none');
            if (mobileClearForm2) mobileClearForm2.classList.add('d-none');
        }

        // تحديث قائمة المتاجر المحددة
        const mobileStoreList = document.getElementById('mobile-store-list');
        if (mobileStoreList && store.list_id) {
            mobileStoreList.value = store.list_id;
        }

        // تحديث حقل عنوان المتجر
        const mobileStoreFullAddress = document.getElementById('mobileStoreFullAddress');
        if (mobileStoreFullAddress && store.full_address) {
            mobileStoreFullAddress.value = store.full_address;
        }

        // تحديث حقل وصف المكان
        const mobileStoreAddress = document.getElementById('mobileStoreAddress');
        if (mobileStoreAddress && store.address) {
            mobileStoreAddress.value = store.address;
        }
    }

    /**
     * Find a store by its ID
     * @param {string|number} storeId - The ID of the store to find
     * @returns {Object|null} The found store or null if not found
     */
    findStoreById(storeId) {
        if (!storeId) return null;

        // تحويل معرف المتجر إلى نص للمقارنة
        const storeIdStr = String(storeId).trim();

        // البحث عن المتجر باستخدام المقارنة النصية
        for (const store of this.stores) {
            if (String(store.id).trim() === storeIdStr) {
                return store;
            }
        }

        console.warn(`Store with ID ${storeId} not found`);
        return null;
    }

    /**
     * Get selected store IDs
     * @returns {Array} Array of selected store IDs
     */
    getSelectedStoreIds() {
        try {
            const selectedStores = [];

            // الحصول على جميع خانات الاختيار المحددة
            const checkboxes = document.querySelectorAll('.store-checkbox:checked');
            console.log('Found checked checkboxes:', checkboxes.length);

            // طباعة جميع خانات الاختيار للتصحيح
            checkboxes.forEach((checkbox, index) => {
                console.log(`Checkbox ${index}:`, checkbox);
                console.log(`  - value:`, checkbox.value);
                console.log(`  - id:`, checkbox.id);
                console.log(`  - checked:`, checkbox.checked);
            });

            // التحقق من كل خانة اختيار وإضافة قيمتها إلى القائمة
            for (let i = 0; i < checkboxes.length; i++) {
                const checkbox = checkboxes[i];
                if (checkbox.checked && checkbox.value && checkbox.value.trim() !== '') {
                    const storeId = checkbox.value.trim();
                    selectedStores.push(storeId);
                    console.log('Added store ID to selection:', storeId);

                    // التحقق من وجود المتجر في قائمة المتاجر
                    const store = this.findStoreById(storeId);
                    if (store) {
                        console.log('  - Found matching store:', store.name);
                    } else {
                        console.warn('  - No matching store found for ID:', storeId);
                    }
                } else {
                    console.warn('Checkbox has invalid value or is not checked:', checkbox);
                }
            }

            if (selectedStores.length === 0) {
                console.warn('No stores selected or checkboxes not found');
            }

            console.log('Final selected store IDs:', selectedStores);
            return selectedStores;
        } catch (error) {
            console.error('Error getting selected store IDs:', error);
            return [];
        }
    }

    /**
     * Update selected stores count
     */
    updateSelectedStoresCount() {
        const checkboxes = document.querySelectorAll('.store-checkbox:checked');
        const count = checkboxes.length;
        const countElem = document.getElementById('selectedStoresCount');
        const shareButton = document.getElementById('shareSelectedStores');
        const deleteButton = document.getElementById('deleteSelectedStores');
        const cancelSelectionBtn = document.getElementById('cancelSelection');

        console.log('Updating selected stores count:', count);

        if (countElem) {
            countElem.textContent = count;
        } else {
            console.warn('Selected stores count element not found');
        }

        if (shareButton) {
            if (count > 0) {
                shareButton.disabled = false;
                shareButton.classList.add('btn-success');
                shareButton.classList.remove('btn-outline-success');
            } else {
                shareButton.disabled = true;
                shareButton.classList.remove('btn-success');
                shareButton.classList.add('btn-outline-success');
            }
        } else {
            console.warn('Share button element not found');
        }

        // تحديث زر الحذف الجماعي
        if (deleteButton) {
            if (count > 0) {
                deleteButton.disabled = false;
                deleteButton.classList.add('btn-danger');
                deleteButton.classList.remove('btn-outline-danger');
            } else {
                deleteButton.disabled = true;
                deleteButton.classList.remove('btn-danger');
                deleteButton.classList.add('btn-outline-danger');
            }
        }

        // تحديث زر إلغاء التحديد
        if (cancelSelectionBtn) {
            if (count > 0) {
                // إظهار زر إلغاء التحديد
                cancelSelectionBtn.classList.remove('d-none');
                cancelSelectionBtn.classList.add('show');
            } else {
                // إخفاء زر إلغاء التحديد
                setTimeout(() => {
                    cancelSelectionBtn.classList.remove('show');
                    setTimeout(() => {
                        cancelSelectionBtn.classList.add('d-none');
                    }, 300); // انتظار انتهاء تأثير الانتقال
                }, 0);
            }
        }

        // تحديث زر تحديد الكل
        const selectAllBtn = document.getElementById('selectAll');
        if (selectAllBtn) {
            const totalStores = document.querySelectorAll('.store-checkbox').length;
            if (count > 0 && count < totalStores) {
                // إظهار زر تحديد الكل فقط إذا كان هناك متاجر محددة ولكن ليس كلها
                selectAllBtn.classList.remove('d-none');
                selectAllBtn.classList.add('show');
            } else {
                // إخفاء زر تحديد الكل
                setTimeout(() => {
                    selectAllBtn.classList.remove('show');
                    setTimeout(() => {
                        selectAllBtn.classList.add('d-none');
                    }, 300); // انتظار انتهاء تأثير الانتقال
                }, 0);
            }
        }

        // تحديث المتاجر المحددة بصريًا
        document.querySelectorAll('.store-card').forEach(card => {
            if (checkboxes.length > 0) {
                const storeId = card.dataset.storeId;
                const isChecked = Array.from(checkboxes).some(cb => cb.value === storeId);

                if (isChecked) {
                    card.classList.add('selected-store');
                } else {
                    card.classList.remove('selected-store');
                }
            } else {
                card.classList.remove('selected-store');
            }
        });
    }

    /**
     * Search stores by name or phone number
     * @param {string} query - The search query
     */
    searchStores(query) {
        console.log('Searching stores with query:', query);

        // التبديل إلى تبويب قائمة المتاجر عند البحث
        if (query && query.trim() !== '' && document.getElementById('list-dropdown-menu')) {
            // النقر على زر جميع القوائم للبحث في جميع المتاجر
            const allListsButton = document.getElementById('list-tab-all');
            if (allListsButton && this.currentListId !== null) {
                allListsButton.click();
                return; // سيتم استدعاء البحث مرة أخرى بعد تحميل جميع المتاجر
            }
        }

        // التحقق من وجود بطاقات المتاجر في الصفحة
        const storeCards = document.querySelectorAll('.store-card');

        // إذا كان البحث فارغًا، عرض جميع المتاجر
        if (!query || query.trim() === '') {
            // إظهار جميع البطاقات وإزالة التمييز
            storeCards.forEach(card => {
                card.style.display = 'block';

                // إزالة التمييز من النص
                const nameElement = card.querySelector('.card-title');
                const phoneElement = card.querySelector('.card-subtitle');

                if (nameElement) {
                    const store = this.findStoreById(card.dataset.storeId);
                    if (store) {
                        nameElement.textContent = store.name || 'متجر بدون اسم';
                    }
                }

                if (phoneElement) {
                    const store = this.findStoreById(card.dataset.storeId);
                    if (store) {
                        phoneElement.textContent = store.phone || 'لا يوجد رقم هاتف';
                    }
                }
            });

            // تحديث عدد المتاجر المعروضة
            const storeCountEl = document.getElementById('storeCount');
            if (storeCountEl) {
                storeCountEl.textContent = storeCards.length;
            }

            // إزالة رسالة عدم وجود نتائج إذا كانت موجودة
            const noResultsMsg = document.getElementById('no-results-message');
            if (noResultsMsg) {
                noResultsMsg.remove();
            }

            return;
        }

        // تنفيذ البحث المحلي
        const normalizedQuery = query.toLowerCase().trim();
        const queryWords = normalizedQuery.split(/\s+/).filter(word => word.length > 0);

        let visibleCount = 0;

        // البحث في بطاقات المتاجر الموجودة
        storeCards.forEach(card => {
            const storeId = card.dataset.storeId;
            const store = this.findStoreById(storeId);

            if (store) {
                const storeName = (store.name || '').toLowerCase();
                const storePhone = (store.phone || '').toLowerCase();

                // التحقق من تطابق جميع كلمات البحث مع اسم المتجر أو رقم الهاتف
                const allWordsMatch = queryWords.every(word =>
                    storeName.includes(word) || storePhone.includes(word)
                );

                // إظهار أو إخفاء البطاقة بناءً على نتيجة البحث
                card.style.display = allWordsMatch ? 'block' : 'none';

                if (allWordsMatch) {
                    visibleCount++;

                    // تمييز كلمات البحث في النص
                    const nameElement = card.querySelector('.card-title');
                    const phoneElement = card.querySelector('.card-subtitle');

                    if (nameElement && store.name) {
                        let nameText = store.name;
                        queryWords.forEach(word => {
                            const regex = new RegExp(word, 'gi');
                            nameText = nameText.replace(regex, match => `<mark>${match}</mark>`);
                        });
                        nameElement.innerHTML = nameText;
                    }

                    if (phoneElement && store.phone) {
                        let phoneText = store.phone;
                        queryWords.forEach(word => {
                            const regex = new RegExp(word, 'gi');
                            phoneText = phoneText.replace(regex, match => `<mark>${match}</mark>`);
                        });
                        phoneElement.innerHTML = phoneText;
                    }
                }
            }
        });

        console.log('Search results:', visibleCount, 'stores found');

        // تحديث عدد المتاجر المعروضة
        const storeCountEl = document.getElementById('storeCount');
        if (storeCountEl) {
            storeCountEl.textContent = visibleCount;
        }

        // إظهار رسالة إذا لم يتم العثور على نتائج
        if (visibleCount === 0) {
            // إذا لم توجد نتائج بحث، أضف رسالة
            if (!document.getElementById('no-results-message')) {
                const noResultsMsg = document.createElement('div');
                noResultsMsg.id = 'no-results-message';
                noResultsMsg.className = 'alert alert-info mt-3';
                noResultsMsg.innerHTML = `<i class="fas fa-search me-2"></i> لا توجد نتائج تطابق "${query}"`;
                this.storeListContainer.prepend(noResultsMsg);
            } else {
                document.getElementById('no-results-message').innerHTML =
                    `<i class="fas fa-search me-2"></i> لا توجد نتائج تطابق "${query}"`;
            }
        } else {
            // إزالة رسالة عدم وجود نتائج إذا كانت موجودة
            const noResultsMsg = document.getElementById('no-results-message');
            if (noResultsMsg) {
                noResultsMsg.remove();
            }
        }
    }
}
