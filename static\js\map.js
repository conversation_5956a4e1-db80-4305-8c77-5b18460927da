/**
 * StoreMap class for handling map operations using Leaflet.js
 */
class StoreMap {
    /**
     * Initialize the map
     * @param {string} containerId - The ID of the container element for the map
     */
    constructor(containerId) {
        // الموقع الافتراضي (طرابلس، ليبيا)
        this.defaultLocation = [32.8872, 13.1913];
        this.defaultZoom = 13;

        // Initialize the map with dragging and touch zoom enabled
        this.map = L.map(containerId, {
            dragging: true,
            touchZoom: true,
            scrollWheelZoom: true,
            doubleClickZoom: true,
            boxZoom: true,
            tap: true,
            keyboard: true,
            zoomControl: true,
            preferCanvas: false,
            renderer: L.svg({ padding: 0.5 })
        }).setView(this.defaultLocation, this.defaultZoom);

        // إصلاح مشكلة التحميل الجزئي للخريطة
        setTimeout(() => {
            this.map.invalidateSize();
            console.log('🔧 إصلاح أولي لحجم الخريطة');
        }, 100);

        // إنشاء طبقات الخريطة
        this.osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        });

        this.satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: 'Tiles &copy; Esri'
        });

        // إضافة الطبقة الافتراضية (خريطة عادية)
        this.osmLayer.addTo(this.map);

        // إصلاح حجم الخريطة بعد تحميل الطبقة
        this.osmLayer.on('load', () => {
            setTimeout(() => {
                this.map.invalidateSize();
                console.log('🔧 إصلاح حجم الخريطة بعد تحميل الطبقة');
            }, 200);
        });

        // إنشاء قائمة الطبقات
        const baseMaps = {
            "خريطة عادية": this.osmLayer,
            "قمر صناعي": this.satelliteLayer
        };

        // إنشاء متحكم الطبقات بشكل مخصص
        this.layersControl = L.control.layers(baseMaps, null, {
            position: 'topright',
            collapsed: true, // جعل القائمة مطوية افتراضيًا
            hideSingleBase: false
        }).addTo(this.map);

        // إضافة أيقونة الطبقات إلى زر التبديل
        this.addLayersButtonIcon();
    }

    /**
     * إضافة أيقونة الطبقات إلى زر التبديل
     */
    addLayersButtonIcon() {
        // تخصيص مظهر زر الطبقات
        setTimeout(() => {
            // إضافة أيقونات للأزرار
            const layersContainer = document.querySelector('.leaflet-control-layers-base');
            if (layersContainer) {
                const labels = layersContainer.querySelectorAll('label');

                // إضافة أيقونة للخريطة العادية
                if (labels[0]) {
                    const mapIcon = document.createElement('i');
                    mapIcon.className = 'fas fa-map me-2';
                    labels[0].querySelector('span').prepend(mapIcon);
                    labels[0].classList.add('map-layer-option');
                }

                // إضافة أيقونة للقمر الصناعي
                if (labels[1]) {
                    const satelliteIcon = document.createElement('i');
                    satelliteIcon.className = 'fas fa-satellite me-2';
                    labels[1].querySelector('span').prepend(satelliteIcon);
                    labels[1].classList.add('map-layer-option');
                }

                // تحسين مظهر متحكم الطبقات
                const layersToggle = document.querySelector('.leaflet-control-layers-toggle');
                if (layersToggle) {
                    // تغيير الأيقونة لتكون مشابهة لخرائط جوجل
                    layersToggle.innerHTML = '<div class="satellite-layer"></div>';
                    layersToggle.title = 'طبقات الخريطة';

                    // إضافة تأثير عند التحويم
                    layersToggle.addEventListener('mouseover', function() {
                        this.style.backgroundColor = '#f8f8f8';
                        this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.3)';
                        this.style.transform = 'translateY(-2px)';
                    });

                    layersToggle.addEventListener('mouseout', function() {
                        this.style.backgroundColor = 'white';
                        this.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.3)';
                        this.style.transform = 'translateY(0)';
                    });
                }

                // إضافة ميزة التصغير التلقائي عند تحريك الخريطة أو الضغط عليها
                this.map.on('movestart', () => {
                    // إغلاق قائمة الطبقات عند تحريك الخريطة
                    const layersControl = document.querySelector('.leaflet-control-layers.leaflet-control-layers-expanded');
                    if (layersControl) {
                        layersControl.classList.remove('leaflet-control-layers-expanded');
                    }
                });

                // إغلاق قائمة الطبقات عند النقر على الخريطة
                this.map.on('click', () => {
                    const layersControl = document.querySelector('.leaflet-control-layers.leaflet-control-layers-expanded');
                    if (layersControl) {
                        layersControl.classList.remove('leaflet-control-layers-expanded');
                    }
                });
            }
        }, 100);

        // Initialize markers layer group
        this.markers = {};
        this.markersLayer = L.layerGroup().addTo(this.map);

        // Keep track of selected location
        this.selectedLocation = null;

        // Set up click handler to select location
        this.setupClickHandler();

        // Set up marker for selected location
        this.selectedMarker = null;

        // مرجع لخريطة الموبايل
        this.mobileMap = null;
    }

    /**
     * تهيئة خريطة الموبايل
     * @param {string} containerId - معرف عنصر حاوية الخريطة
     */
    initMobileMap(containerId) {
        // تهيئة خريطة الموبايل مع تمكين جميع التفاعلات
        this.mobileMap = L.map(containerId, {
            dragging: true,
            touchZoom: true,
            scrollWheelZoom: true,
            doubleClickZoom: true,
            boxZoom: true,
            tap: true,
            keyboard: true,
            zoomControl: true
        }).setView(this.defaultLocation, this.defaultZoom);

        // إنشاء طبقات الخريطة للموبايل
        this.mobileOsmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        });

        this.mobileSatelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
            attribution: 'Tiles &copy; Esri'
        });

        // إضافة الطبقة الافتراضية (خريطة عادية)
        this.mobileOsmLayer.addTo(this.mobileMap);

        // إنشاء قائمة الطبقات
        const mobileBaseMaps = {
            "خريطة عادية": this.mobileOsmLayer,
            "قمر صناعي": this.mobileSatelliteLayer
        };

        // إنشاء متحكم الطبقات بشكل مخصص للهاتف
        this.mobilelayersControl = L.control.layers(mobileBaseMaps, null, {
            position: 'topright',
            collapsed: true, // جعل القائمة مطوية افتراضيًا
            hideSingleBase: false
        }).addTo(this.mobileMap);

        // إضافة أيقونة الطبقات لزر الموبايل
        setTimeout(() => {
            const mobileLayersButton = document.querySelector('#' + containerId + ' .leaflet-control-layers-toggle');
            if (mobileLayersButton) {
                // تغيير الأيقونة لتكون مشابهة لخرائط جوجل
                mobileLayersButton.style.backgroundImage = 'none';
                mobileLayersButton.innerHTML = '<div class="satellite-layer"></div>';
                mobileLayersButton.title = 'طبقات الخريطة';
            }
        }, 100);

        // تخصيص مظهر زر الطبقات للهاتف
        setTimeout(() => {
            // إضافة أيقونات للأزرار
            const mobileLayersContainers = document.querySelectorAll('.leaflet-control-layers-base');
            if (mobileLayersContainers.length > 1) {
                const mobileLayersContainer = mobileLayersContainers[1]; // الثاني هو للهاتف
                const labels = mobileLayersContainer.querySelectorAll('label');

                // إضافة أيقونة للخريطة العادية
                if (labels[0]) {
                    const mapIcon = document.createElement('i');
                    mapIcon.className = 'fas fa-map me-2';
                    labels[0].querySelector('span').prepend(mapIcon);
                }

                // إضافة أيقونة للقمر الصناعي
                if (labels[1]) {
                    const satelliteIcon = document.createElement('i');
                    satelliteIcon.className = 'fas fa-satellite me-2';
                    labels[1].querySelector('span').prepend(satelliteIcon);
                }

                // تطبيق تنسيقات CSS إضافية
                labels.forEach(label => {
                    label.style.display = 'flex';
                    label.style.alignItems = 'center';
                    label.style.padding = '8px 12px';
                    label.style.margin = '4px 0';
                    label.style.borderRadius = '4px';
                    label.style.transition = 'all 0.3s ease';
                    label.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';

                    // إضافة تأثير عند التحويم
                    label.addEventListener('mouseover', () => {
                        label.style.backgroundColor = 'rgba(255, 255, 255, 0.9)';
                        label.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.1)';
                    });

                    label.addEventListener('mouseout', () => {
                        label.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
                        label.style.boxShadow = 'none';
                    });
                });
            }
        }, 200);

        // إضافة مستمع أحداث للنقر على الخريطة مع التحقق من عدم التحريك
        let mobileIsDragging = false;

        this.mobileMap.on('dragstart', () => {
            mobileIsDragging = true;
        });

        this.mobileMap.on('dragend', () => {
            // إعادة تعيين المتغير بعد فترة قصيرة
            setTimeout(() => {
                mobileIsDragging = false;
            }, 50);
        });

        this.mobileMap.on('click', (e) => {
            // التحقق من أن الخريطة لم يتم تحريكها قبل النقر
            if (!mobileIsDragging) {
                this.setSelectedLocation(e.latlng);
            }
        });

        // مزامنة الخريطتين مع تجنب حلقة لا نهائية
        this.isSyncing = false; // متغير لمنع التزامن المتكرر

        this.map.on('moveend', () => {
            if (this.mobileMap && !this.isSyncing) {
                this.isSyncing = true;
                this.mobileMap.setView(this.map.getCenter(), this.map.getZoom(), { animate: false });
                setTimeout(() => {
                    this.isSyncing = false;
                }, 100);
            }
        });

        this.mobileMap.on('moveend', () => {
            if (!this.isSyncing) {
                this.isSyncing = true;
                this.map.setView(this.mobileMap.getCenter(), this.mobileMap.getZoom(), { animate: false });
                setTimeout(() => {
                    this.isSyncing = false;
                }, 100);
            }
        });

        // مزامنة نوع الخريطة بين الكمبيوتر والهاتف
        this.map.on('baselayerchange', (e) => {
            if (this.mobileMap) {
                // إذا تم تغيير الخريطة إلى القمر الصناعي
                if (e.name === "قمر صناعي") {
                    // إزالة الطبقة الحالية وإضافة طبقة القمر الصناعي
                    if (this.mobileMap.hasLayer(this.mobileOsmLayer)) {
                        this.mobileMap.removeLayer(this.mobileOsmLayer);
                    }
                    this.mobileMap.addLayer(this.mobileSatelliteLayer);
                } else {
                    // إزالة الطبقة الحالية وإضافة طبقة الخريطة العادية
                    if (this.mobileMap.hasLayer(this.mobileSatelliteLayer)) {
                        this.mobileMap.removeLayer(this.mobileSatelliteLayer);
                    }
                    this.mobileMap.addLayer(this.mobileOsmLayer);
                }
            }
        });

        this.mobileMap.on('baselayerchange', (e) => {
            // إذا تم تغيير الخريطة إلى القمر الصناعي
            if (e.name === "قمر صناعي") {
                // إزالة الطبقة الحالية وإضافة طبقة القمر الصناعي
                if (this.map.hasLayer(this.osmLayer)) {
                    this.map.removeLayer(this.osmLayer);
                }
                this.map.addLayer(this.satelliteLayer);
            } else {
                // إزالة الطبقة الحالية وإضافة طبقة الخريطة العادية
                if (this.map.hasLayer(this.satelliteLayer)) {
                    this.map.removeLayer(this.satelliteLayer);
                }
                this.map.addLayer(this.osmLayer);
            }
        });
    }

    /**
     * Set up click handler for selecting location on map
     */
    setupClickHandler() {
        // إضافة متغير للتحقق من أن الخريطة لم يتم تحريكها
        let isDragging = false;

        this.map.on('dragstart', () => {
            isDragging = true;
        });

        this.map.on('dragend', () => {
            // إعادة تعيين المتغير بعد فترة قصيرة للسماح بالنقر بعد التحريك
            setTimeout(() => {
                isDragging = false;
            }, 50);
        });

        this.map.on('click', (e) => {
            // التحقق من أن الخريطة لم يتم تحريكها قبل النقر
            if (!isDragging) {
                this.setSelectedLocation(e.latlng);
            }
        });
    }

    /**
     * Set the selected location and update the marker
     * @param {L.LatLng} latlng - The latitude and longitude of the selected location
     */
    setSelectedLocation(latlng) {
        console.log('Setting selected location:', latlng);
        this.selectedLocation = latlng;

        // Remove existing selected marker if any
        if (this.selectedMarker) {
            try {
                this.map.removeLayer(this.selectedMarker);
            } catch (error) {
                console.error('Error removing marker:', error);
            }
        }

        // Remove existing mobile selected marker if any
        if (this.mobileSelectedMarker && this.mobileMap) {
            try {
                this.mobileMap.removeLayer(this.mobileSelectedMarker);
            } catch (error) {
                console.error('Error removing mobile marker:', error);
            }
        }

        // Create marker icon
        const markerIcon = L.divIcon({
            className: 'selected-marker',
            html: '<i class="fas fa-map-pin text-danger" style="font-size: 24px;"></i>',
            iconSize: [24, 24],
            iconAnchor: [12, 24]
        });

        // Add new marker for selected location on main map
        this.selectedMarker = L.marker(latlng, { icon: markerIcon }).addTo(this.map);

        // Add new marker for selected location on mobile map if it exists
        if (this.mobileMap) {
            this.mobileSelectedMarker = L.marker(latlng, { icon: markerIcon }).addTo(this.mobileMap);
        }

        // Update form with coordinates
        const selectedLocationEl = document.getElementById('selectedLocation');
        if (selectedLocationEl) {
            selectedLocationEl.textContent = `الموقع المحدد: ${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
            selectedLocationEl.classList.remove('text-muted');
            selectedLocationEl.classList.add('text-info');
        }

        // Update mobile form with coordinates
        const mobileSelectedLocationEl = document.getElementById('mobileSelectedLocation');
        if (mobileSelectedLocationEl) {
            mobileSelectedLocationEl.textContent = `الموقع المحدد: ${latlng.lat.toFixed(6)}, ${latlng.lng.toFixed(6)}`;
            mobileSelectedLocationEl.classList.remove('text-muted');
            mobileSelectedLocationEl.classList.add('text-info');
        }
    }

    /**
     * Get the current selected location
     * @returns {L.LatLng|null} The selected location or null if none selected
     */
    getSelectedLocation() {
        return this.selectedLocation;
    }

    /**
     * الحصول على الموقع الحالي للمستخدم بدقة عالية
     * @returns {Promise} وعد بموقع المستخدم
     */
    getCurrentLocation() {
        return new Promise((resolve, reject) => {
            if (navigator.geolocation) {
                // إظهار مؤشر التحميل
                showAlert('جاري تحديد موقعك...', 'info');

                // استخدام خيارات مخصصة للحصول على دقة أعلى
                navigator.geolocation.getCurrentPosition(
                    {
                        enableHighAccuracy: true, // تمكين الدقة العالية
                        timeout: 10000,          // مهلة زمنية 10 ثواني
                        maximumAge: 0            // عدم استخدام موقع مخزن مسبقًا
                    },

                    // نجاح
                    (position) => {
                        const latlng = L.latLng(position.coords.latitude, position.coords.longitude);
                        const accuracy = position.coords.accuracy; // دقة الموقع بالمتر

                        // تحريك الخريطة إلى الموقع الحالي مع ضبط مستوى التكبير بناءً على الدقة
                        const zoom = accuracy <= 50 ? 18 : (accuracy <= 500 ? 16 : 15);

                        this.map.setView(latlng, zoom);
                        if (this.mobileMap) {
                            this.mobileMap.setView(latlng, zoom);
                        }

                        // إضافة دائرة توضح نطاق الدقة
                        if (this.accuracyCircle) {
                            this.map.removeLayer(this.accuracyCircle);
                            if (this.mobileMap) {
                                this.mobileMap.removeLayer(this.mobileAccuracyCircle);
                            }
                        }

                        this.accuracyCircle = L.circle(latlng, {
                            radius: accuracy,
                            color: '#3388ff',
                            fillColor: '#3388ff',
                            fillOpacity: 0.1,
                            weight: 1
                        }).addTo(this.map);

                        if (this.mobileMap) {
                            this.mobileAccuracyCircle = L.circle(latlng, {
                                radius: accuracy,
                                color: '#3388ff',
                                fillColor: '#3388ff',
                                fillOpacity: 0.1,
                                weight: 1
                            }).addTo(this.mobileMap);
                        }

                        this.setSelectedLocation(latlng);

                        showAlert(`تم تحديد موقعك الحالي (دقة: ${Math.round(accuracy)} متر)`, 'success');
                        resolve(latlng);
                    },
                    // فشل
                    (error) => {
                        console.error('Error getting current location:', error);

                        let errorMessage = 'تعذر الحصول على موقعك الحالي.';
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                errorMessage = 'تم رفض الوصول إلى الموقع. يرجى تمكين خدمات الموقع في متصفحك.';
                                break;
                            case error.POSITION_UNAVAILABLE:
                                errorMessage = 'معلومات الموقع غير متاحة.';
                                break;
                            case error.TIMEOUT:
                                errorMessage = 'انتهت مهلة طلب الموقع.';
                                break;
                        }

                        showAlert(errorMessage, 'danger');
                        reject(error);
                    }
                );
            } else {
                const error = new Error('خدمة تحديد الموقع غير مدعومة في متصفحك');
                showAlert(error.message, 'danger');
                reject(error);
            }
        });
    }

    /**
     * Clear all markers from the map
     */
    clearMarkers() {
        // إزالة جميع العلامات من الخريطة الرئيسية
        for (const id in this.markers) {
            try {
                // التحقق مما إذا كانت العلامة تنتمي إلى الخريطة الرئيسية أو خريطة الموبايل
                if (id.startsWith('mobile_')) {
                    if (this.mobileMap) {
                        this.mobileMap.removeLayer(this.markers[id]);
                    }
                } else {
                    this.map.removeLayer(this.markers[id]);
                }
            } catch (error) {
                console.warn(`Error removing marker ${id}:`, error);
            }
        }

        // إعادة تعيين قائمة العلامات
        this.markers = {};

        // إزالة العلامة المحددة إذا كانت موجودة
        if (this.selectedMarker) {
            try {
                this.map.removeLayer(this.selectedMarker);
                this.selectedMarker = null;
            } catch (error) {
                console.warn('Error removing selected marker:', error);
            }
        }

        // إزالة العلامة المحددة من خريطة الموبايل إذا كانت موجودة
        if (this.mobileMap && this.mobileSelectedMarker) {
            try {
                this.mobileMap.removeLayer(this.mobileSelectedMarker);
                this.mobileSelectedMarker = null;
            } catch (error) {
                console.warn('Error removing mobile selected marker:', error);
            }
        }
    }

    /**
     * Add a marker for a store to the map
     * @param {Object} store - The store object containing lat, lng and other properties
     * @param {Function} onClick - The function to call when the marker is clicked
     */
    addStoreMarker(store, onClick) {
        console.log('Adding marker for store:', store);

        // إنشاء أيقونة مخصصة للمتجر
        const storeIcon = L.divIcon({
            className: 'store-marker',
            html: '<i class="fas fa-map-marker-alt" style="font-size: 24px; color: #d50000;"></i>',
            iconSize: [24, 24],
            iconAnchor: [12, 24]
        });

        // التحقق من وجود إحداثيات المتجر
        const lat = store.latitude || store.lat;
        const lng = store.longitude || store.lng;

        console.log('Store coordinates:', lat, lng);

        if (!lat || !lng) {
            console.error('Store has no valid coordinates:', store);
            return null;
        }

        // إنشاء علامة جديدة
        const marker = L.marker([lat, lng], {
            icon: storeIcon
        });

        // إضافة معلومات المتجر إلى العلامة
        // إصلاح مسار الصورة - إزالة /static/ لأن image_path يتضمن بالفعل static/
        const imagePath = store.image_path ? `/${store.image_path}` : (store.imageUrl || '');
        const phone = store.phone || '';

        // تحديد معلومات الموقع
        let locationInfo = '';
        if (store.city_name && store.region_name) {
            locationInfo = `${store.city_name} - ${store.region_name}`;
        } else if (store.city_name) {
            locationInfo = store.city_name;
        } else if (store.region_name) {
            locationInfo = store.region_name;
        } else {
            locationInfo = '<span style="color: #dc3545; font-weight: 500;">المنطقة غير محددة</span>';
        }

        const popupContent = `
            <div class="store-popup">
                <h5>${store.name}</h5>
                <p class="text-muted small mb-1">${locationInfo}</p>
                ${phone ? `<p><i class="fas fa-phone"></i> ${phone}</p>` : ''}
                ${imagePath ? `<img src="${imagePath}" class="img-fluid mb-2" style="max-height: 100px;">` : ''}
                <div class="d-grid gap-2">
                    <button class="btn btn-sm btn-primary edit-store-btn" data-store-id="${store.id}">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn btn-sm btn-success share-whatsapp" data-store-id="${store.id}">
                        <i class="fab fa-whatsapp"></i> مشاركة
                    </button>
                </div>
            </div>
        `;

        marker.bindPopup(popupContent);

        // إضافة مستمع أحداث للنقر على العلامة
        marker.on('click', () => {
            marker.openPopup();
            if (typeof onClick === 'function') {
                onClick(store);
            }
        });

        // إضافة مستمع أحداث للنافذة المنبثقة
        marker.getPopup().on('add', () => {
            setTimeout(() => {
                // إضافة مستمعي أحداث لأزرار التعديل والمشاركة
                const editBtn = document.querySelector(`.edit-store-btn[data-store-id="${store.id}"]`);
                if (editBtn) {
                    // إزالة مستمعات الأحداث السابقة لتجنب التكرار
                    const newEditBtn = editBtn.cloneNode(true);
                    editBtn.parentNode.replaceChild(newEditBtn, editBtn);

                    newEditBtn.addEventListener('click', () => {
                        // استدعاء حدث تحديد المتجر
                        const event = new CustomEvent('selectStore', { detail: { storeId: store.id } });
                        document.dispatchEvent(event);
                        marker.closePopup();
                    });
                }

                const shareBtn = document.querySelector(`.share-whatsapp[data-store-id="${store.id}"]`);
                if (shareBtn) {
                    // إزالة مستمعات الأحداث السابقة لتجنب التكرار
                    const newShareBtn = shareBtn.cloneNode(true);
                    shareBtn.parentNode.replaceChild(newShareBtn, shareBtn);

                    newShareBtn.addEventListener('click', () => {
                        this.shareStoreViaWhatsApp(store);
                        marker.closePopup();
                    });
                }
            }, 100); // إضافة تأخير للتأكد من أن النافذة المنبثقة قد تم إنشاؤها بالكامل
        });

        // إضافة العلامة إلى الخريطة وتخزينها
        marker.addTo(this.map);
        this.markers[store.id] = marker;

        // إضافة العلامة إلى خريطة الموبايل إذا كانت موجودة
        if (this.mobileMap) {
            const mobileMarker = L.marker([lat, lng], {
                icon: storeIcon
            });

            // إضافة نفس النافذة المنبثقة
            mobileMarker.bindPopup(popupContent);

            // إضافة نفس مستمعات الأحداث
            mobileMarker.on('click', () => {
                mobileMarker.openPopup();
                if (typeof onClick === 'function') {
                    onClick(store);
                }
            });

            // إضافة مستمع أحداث للنافذة المنبثقة
            mobileMarker.getPopup().on('add', () => {
                setTimeout(() => {
                    // إضافة مستمعي أحداث لأزرار التعديل والمشاركة
                    const editBtn = document.querySelector(`.edit-store-btn[data-store-id="${store.id}"]`);
                    if (editBtn) {
                        // إزالة مستمعات الأحداث السابقة لتجنب التكرار
                        const newEditBtn = editBtn.cloneNode(true);
                        editBtn.parentNode.replaceChild(newEditBtn, editBtn);

                        newEditBtn.addEventListener('click', () => {
                            // استدعاء حدث تحديد المتجر
                            const event = new CustomEvent('selectStore', { detail: { storeId: store.id } });
                            document.dispatchEvent(event);
                            mobileMarker.closePopup();
                        });
                    }

                    const shareBtn = document.querySelector(`.share-whatsapp[data-store-id="${store.id}"]`);
                    if (shareBtn) {
                        // إزالة مستمعات الأحداث السابقة لتجنب التكرار
                        const newShareBtn = shareBtn.cloneNode(true);
                        shareBtn.parentNode.replaceChild(newShareBtn, shareBtn);

                        newShareBtn.addEventListener('click', () => {
                            this.shareStoreViaWhatsApp(store);
                            mobileMarker.closePopup();
                        });
                    }
                }, 100); // إضافة تأخير للتأكد من أن النافذة المنبثقة قد تم إنشاؤها بالكامل
            });

            mobileMarker.addTo(this.mobileMap);
            this.markers[`mobile_${store.id}`] = mobileMarker;
        }

        // تخزين مرجع العلامة في كائن المتجر
        store.marker = marker;

        return marker;
    }

    /**
     * Get user's current location and set the map view to it
     */
    getCurrentLocation() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(
                // Success callback
                (position) => {
                    const latlng = L.latLng(position.coords.latitude, position.coords.longitude);
                    this.map.setView(latlng, 15);
                    this.setSelectedLocation(latlng);

                    showAlert('تم العثور على الموقع الحالي', 'success');
                },
                // Error callback
                (error) => {
                    console.error('Error getting current location:', error);

                    let errorMessage = 'تعذر الحصول على موقعك الحالي.';
                    switch (error.code) {
                        case error.PERMISSION_DENIED:
                            errorMessage = 'تم رفض الوصول إلى الموقع. يرجى تمكين خدمات الموقع في متصفحك.';
                            break;
                        case error.POSITION_UNAVAILABLE:
                            errorMessage = 'معلومات الموقع غير متاحة.';
                            break;
                        case error.TIMEOUT:
                            errorMessage = 'انتهت مهلة طلب الموقع.';
                            break;
                    }

                    showAlert(errorMessage, 'danger');
                }
            );
        } else {
            showAlert('خدمة تحديد الموقع غير مدعومة في متصفحك', 'danger');
        }
    }

    /**
     * Fly to a specific store on the map
     * @param {Object} store - The store object containing lat and lng properties
     * @returns {boolean} - Whether the operation was successful
     */
    flyToStore(store) {
        const lat = store.latitude || store.lat;
        const lng = store.longitude || store.lng;

        // التحقق من وجود موقع للمتجر
        if (!lat || !lng) {
            console.warn('Store has no location:', store);
            return false;
        }

        // الانتقال إلى الموقع بتأثير الطيران
        this.map.flyTo([lat, lng], 15);

        // الانتقال إلى الموقع في خريطة الموبايل إذا كانت موجودة
        if (this.mobileMap) {
            this.mobileMap.flyTo([lat, lng], 15);
        }

        // فتح النافذة المنبثقة إذا كانت العلامة موجودة
        if (store.marker) {
            store.marker.openPopup();
        } else if (this.markers[store.id]) {
            this.markers[store.id].openPopup();
        }

        return true;
    }

    /**
     * Create WhatsApp sharing link for a store location
     * @param {Object} store - The store object
     * @returns {string} WhatsApp sharing URL
     */
    createWhatsAppShareLink(store) {
        try {
            // التأكد من وجود بيانات المتجر
            if (!store || !store.name) {
                console.error('Invalid store data for WhatsApp sharing', store);
                return 'https://wa.me/';
            }

            // إنشاء نص المشاركة
            const storeName = store.name || 'متجر';

            // إضافة عنوان المتجر
            let storeAddress = '';
            if (store.city_name && store.region_name) {
                storeAddress = `${store.city_name} - ${store.region_name}`;
            } else if (store.address) {
                storeAddress = store.address;
            }

            const storeAddressText = storeAddress ? `\nالعنوان: ${storeAddress}` : '';
            const storePhone = store.phone ? `\nالهاتف: ${store.phone}` : '';
            const lat = store.latitude || store.lat;
            const lng = store.longitude || store.lng;
            const mapLink = `https://www.google.com/maps?q=${lat},${lng}`;

            const text = `إلقِ نظرة على هذا المتجر: ${storeName}${storeAddressText}${storePhone}\nالموقع: ${mapLink}`;

            // إنشاء رابط واتساب
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
            console.log('Created WhatsApp URL:', whatsappUrl);

            return whatsappUrl;
        } catch (error) {
            console.error('Error creating WhatsApp share link:', error);
            return 'https://wa.me/';
        }
    }

    /**
     * Share a store via WhatsApp
     * @param {Object} store - The store object to share
     * @returns {boolean} Whether the sharing was successful
     */
    shareStoreViaWhatsApp(store) {
        try {
            if (!store) {
                console.error('No store provided for WhatsApp sharing');
                return false;
            }

            // إنشاء نص المشاركة
            const storeName = store.name || 'متجر';

            // إضافة عنوان المتجر
            let storeAddress = '';
            if (store.city_name && store.region_name) {
                storeAddress = `${store.city_name} - ${store.region_name}`;
            } else if (store.address) {
                storeAddress = store.address;
            }

            const storeAddressText = storeAddress ? `\nالعنوان: ${storeAddress}` : '';
            const storePhone = store.phone ? `\nالهاتف: ${store.phone}` : '';
            const lat = store.latitude || store.lat;
            const lng = store.longitude || store.lng;
            const mapLink = `https://www.google.com/maps?q=${lat},${lng}`;

            const text = `إلقِ نظرة على هذا المتجر: ${storeName}${storeAddressText}${storePhone}\nالموقع: ${mapLink}`;

            // فتح واتساب بالنص
            return this.openWhatsApp(text);
        } catch (error) {
            console.error('Error sharing store via WhatsApp:', error);
            return false;
        }
    }

    /**
     * Open WhatsApp with the given text
     * @param {string} text - The text to share on WhatsApp
     * @returns {boolean} Whether the opening was successful
     */
    openWhatsApp(text) {
        try {
            if (!text) {
                console.error('No text provided for WhatsApp sharing');
                return false;
            }

            // إنشاء رابط واتساب
            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text)}`;
            console.log('Opening WhatsApp with URL:', whatsappUrl);

            // إنشاء رابط مباشر في الصفحة
            const linkElement = document.createElement('a');
            linkElement.href = whatsappUrl;
            linkElement.target = '_blank';
            linkElement.rel = 'noopener noreferrer';
            linkElement.style.display = 'none';
            document.body.appendChild(linkElement);

            // محاكاة النقر على الرابط
            linkElement.click();

            // إزالة الرابط من الصفحة
            setTimeout(() => {
                document.body.removeChild(linkElement);
                console.log('WhatsApp link clicked and removed');
            }, 100);

            return true;
        } catch (error) {
            console.error('Error opening WhatsApp:', error);
            return false;
        }
    }


}
